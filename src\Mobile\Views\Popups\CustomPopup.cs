﻿using Microsoft.Maui.Primitives;
using AppoMobi.Mobile.Views.Popups;
using LayoutAlignment = Microsoft.Maui.Primitives.LayoutAlignment;

namespace AppoMobi.Main
{
    public class CustomPopup : PopupPage
    {
        public CustomPopup()
        {
            Color = Colors.Transparent;
            OverlayColor =Color.Parse( "#CC000000");
            HorizontalOptions = LayoutAlignment.Fill;
            VerticalOptions = LayoutAlignment.Fill;
            IgnoreSafeArea = true;
        }
    }

    public class DisposableContent : ContentView, IDisposable
    {
        public void Dispose()
        {
            if (Content is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
    }

}
