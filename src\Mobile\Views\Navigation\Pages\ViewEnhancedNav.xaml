﻿<?xml version="1.0" encoding="utf-8" ?>
<Grid
    x:Class="AppoMobi.Mobile.Views.Navigation.ViewEnhancedNav"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:appoMobi2="clr-namespace:AppoMobi"
    xmlns:maui="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:touch="clr-namespace:AppoMobi.Touch"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="MainGrid"
    BackgroundColor="{x:Static xam:BackColors.Page}"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="FillAndExpand">

    <!--  NavigationPage.BackButtonTitle="{x:Static resX:ResStrings.GoBack}"  -->

    <xam:CGrid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">

        <!--  wallpaper  -->
        <appoMobi2:WallpaperImage x:Name="cWallpaperImage" />

        <svg:GradientBox
            EndColor="{x:Static xam:BackColors.GradientPageFaderStartEnd}"
            HorizontalOptions="FillAndExpand"
            StartColor="{x:Static xam:BackColors.GradientPageFaderStart}"
            VerticalOptions="FillAndExpand" />

    </xam:CGrid>


    <!--  MainScroll with Included Content  -->
    <xam:SolidScrollView x:Name="MainScroll" Scrolled="OnScrolled_ScrollView">
        <StackLayout Spacing="0" VerticalOptions="FillAndExpand">

            <ui:NavBarPadding x:Name="cNavBarPadding" BackgroundColor="Transparent" />

            <!--  insert content here NON FULLSCREEN CONTENT  -->
            <ContentView
                x:Name="cContent"
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand" />
            <!--  end content  -->

        </StackLayout>
    </xam:SolidScrollView>

    <!--  Fullscreen for Included Content - Option - FULLSCREEN CONTENT  -->
    <StackLayout
        x:Name="cFullScreenStack"
        Spacing="0"
        VerticalOptions="FillAndExpand">

        <ui:NavBarPadding x:Name="cNavBarPadding2" BackgroundColor="Transparent" />

        <ContentView
            x:Name="cFullScreen"
            HorizontalOptions="FillAndExpand"
            IsVisible="False"
            VerticalOptions="FillAndExpand">

            <!--  insert content here  -->

        </ContentView>
        <!--  end content  -->
    </StackLayout>

    <!--  Flying over section  -->
    <ContentView
        x:Name="cPopup"
        HorizontalOptions="FillAndExpand"
        IsVisible="False"
        VerticalOptions="FillAndExpand" />

    <!--  LOADING PLS WAIT  -->
    <xam:CGrid
        x:Name="cLoading"
        InputTransparent="True"
        IsVisible="False"
        VerticalOptions="FillAndExpand">

        <xam:Spinner />

    </xam:CGrid>

    <ContentView
        x:Name="cOffline"
        InputTransparent="True"
        IsVisible="False" />

    <!--  OVERLAY WE ARE OFFLINE  -->
    <!--<StackLayout x:Name="cOffline"
                 IsVisible="False"
                 Spacing="0"
                 VerticalOptions="Center"
                 HorizontalOptions="Center">

        <appoMobi:CardView BackgroundColor="{x:Static appoMobi:AppColors.PrimaryDark75}"
                           Margin="16,16,16,16">
          <appoMobi:XLabel
              Margin="16,16,16,16"
              Justify="True"

              LineSpacing="1.0"
              FontSize="14"
              TextColor="{x:Static appoMobi:AppColors.White}"
              Text="{x:Static resX:ResStrings.NeedInternet}" />
        </appoMobi:CardView>

        <controls:CButton
          x:Name="btn1"
            Margin="0,5,0,10"
            Tapped="On_btnConnect"
            Text="{x:Static resX:ResStrings.ButtonConnect}"/>

      </StackLayout>-->

    <!--  NAVIGATION BLOCK  -->
    <xam:CGrid
        x:Name="gridNavi"
        HorizontalOptions="FillAndExpand"
        RowSpacing="0"
        VerticalOptions="Start">
        <Grid.RowDefinitions>
            <RowDefinition x:Name="rowStatusBar" Height="Auto" />
            <RowDefinition x:Name="rowNavBar" Height="Auto" />
        </Grid.RowDefinitions>

        <!--  status bar  -->
        <!--<BoxView
            x:Name="boxStatusBar"
            touch:LegacyGesturesGrid.Row="0"
            BackgroundColor="{x:Static appoMobi:AppColors.Primary}"
            HeightRequest="20"
            HorizontalOptions="FillAndExpand" />-->

        <!--  navigation bar  -->
        <BoxView
            x:Name="boxNavBar"
            Grid.Row="1"
            BackgroundColor="{x:Static appoMobi:AppColors.Primary}"
            HeightRequest="20"
            HorizontalOptions="FillAndExpand" />

        <!--  image  -->
        <ContentView
            x:Name="imgNavigationBack"
            Grid.RowSpan="2"
            HorizontalOptions="Fill"
            IsClippedToBounds="True"
            Opacity="0.9"
            VerticalOptions="Fill">

            <maui:CachedImage
                x:Name="srcNavigationBack"
                Aspect="AspectFill"
                FadeAnimationEnabled="False"
                HorizontalOptions="Fill"
                LoadingPriority="Highest"
                VerticalOptions="Fill" />

        </ContentView>


        <!--  NAVIGATION BAR  -->
        <ui:NavBar
            x:Name="cNavBar"
            Title="{Binding Title}"
            Grid.Row="1"
            FontAttributes="None"
            FontSize="{x:Static xam:FontSizes.NavBarTitleSize}"
            OnDownLeftIcon1="CNavBar_OnDownLeftIcon1"
            OnDownLeftIcon2="CNavBar_OnDownLeftIcon2"
            OnDownRightIcon1="CNavBar_OnDownRightIcon1"
            OnDownRightIcon2="CNavBar_OnDownRightIcon2" />

        <!--  STATUS BAR  -->
        <ui:StatusBar x:Name="cNavStatusBar" 
                      Opacity="0.75"
                      Grid.Row="0" />

    </xam:CGrid>

    <!--  cNavStatusShadowed  -->
    <BoxView
        x:Name="cNavStatusShadowed"
        Grid.Row="0"
        BackgroundColor="{x:Static appoMobi2:AppColors.Site_PanelXX}"
        HeightRequest="{Binding Source={x:Reference cNavStatusBar}, Path=Height}"
        HorizontalOptions="FillAndExpand"
        IsVisible="True"
        VerticalOptions="Start" />

    <!--  OVERLAY COLLAPSED MENU  -->
    <ui:OverlayNavBarIconed
        x:Name="cOverlayNavBar"
        IsVisible="False"
        OnDownLeftIcon1="CNavBar_OnDownLeftIcon1" />

    <!--  OVERLAY BUTTON SCROLL UP  -->
    <ui:OverlayNavBarIconed
        x:Name="cOverlayScrollUp"
        Margin="0,0,10,5"
        HorizontalOptions="EndAndExpand"
        IsVisible="False"
        OnDownLeftIcon1="CNavBar_OnDownIconScrollUp"
        VerticalOptions="End" />
    <!--  &#xf341;  -->

    <ui:Shadowline x:Name="cTabsShadow" VerticalOptions="End" />

</Grid>