﻿using AppoMobi.Mobile.Views.Popups;
using System;
using System.Threading.Tasks;


namespace AppoMobi.UI
{
    public class NiftyPopupDialogBase : PopupPage
    {
        public NiftyPopupDialogBase()
        {
            CanBeDismissedByTappingOutsideOfPopup = true;
        }

        private string _title = "";
        public string Message
        {
            get { return _title; }

            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        public Action<bool> Callback { get; set; } = null;

        public Action<int> CallbackList { get; set; } = null;

        public Action<string> CallbackListKey { get; set; } = null;



        // Invoced when background is clicked
        protected override bool OnBackgroundClicked()

        {
            if (DisableBackgroundClick)
                // Return default value - CloseWhenBackgroundIsClicked
                return base.OnBackgroundClicked();

            return false;
        }

        public bool DisableBackgroundClick { get; set; }
        public bool QuitOnBackPressed { get; set; }





        protected async Task DismissDialog(bool ret)
        {
            Callback(ret);
            Dismiss();
        }

    }
}
