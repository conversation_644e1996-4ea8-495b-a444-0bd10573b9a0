﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
		Microsoft ResX Schema

		Version 1.3

		The primary goals of this format is to allow a simple XML format 
		that is mostly human readable. The generation and parsing of the 
		various data types are done through the TypeConverter classes 
		associated with the data types.

		Example:

		... ado.net/XML headers & schema ...
		<resheader name="resmimetype">text/microsoft-resx</resheader>
		<resheader name="version">1.3</resheader>
		<resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
		<resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
		<data name="Name1">this is my long string</data>
		<data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
		<data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
			[base64 mime encoded serialized .NET Framework object]
		</data>
		<data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
			[base64 mime encoded string representing a byte array form of the .NET Framework object]
		</data>

		There are any number of "resheader" rows that contain simple 
		name/value pairs.

		Each data row contains a name, and value. The row also contains a 
		type or mimetype. Type corresponds to a .NET class that support 
		text/value conversion through the TypeConverter architecture. 
		Classes that don't support this are serialized and stored with the 
		mimetype set.

		The mimetype is used for serialized objects, and tells the 
		ResXResourceReader how to depersist the object. This is currently not 
		extensible. For a given mimetype the value must be set accordingly:

		Note - application/x-microsoft.net.object.binary.base64 is the format 
		that the ResXResourceWriter will generate, however the reader can 
		read any of the formats listed below.

		mimetype: application/x-microsoft.net.object.binary.base64
		value   : The object must be serialized with 
			: System.Serialization.Formatters.Binary.BinaryFormatter
			: and then encoded with base64 encoding.

		mimetype: application/x-microsoft.net.object.soap.base64
		value   : The object must be serialized with 
			: System.Runtime.Serialization.Formatters.Soap.SoapFormatter
			: and then encoded with base64 encoding.

		mimetype: application/x-microsoft.net.object.bytearray.base64
		value   : The object must be serialized into a byte array 
			: using a System.ComponentModel.TypeConverter
			: and then encoded with base64 encoding.
	-->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>1.3</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.3500.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.3500.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Test" xml:space="preserve">
    <value>Prueba</value>
  </data>
  <data name="AddNews" xml:space="preserve">
    <value>Agrega una noticia</value>
  </data>
  <data name="NewsTitleDesc" xml:space="preserve">
    <value>Noticias</value>
  </data>
  <data name="BtnEdit" xml:space="preserve">
    <value>Editar</value>
  </data>
  <data name="BtnDetails" xml:space="preserve">
    <value>Cepa</value>
  </data>
  <data name="BtnDelete" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="News" xml:space="preserve">
    <value>Noticias</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Contactos</value>
  </data>
  <data name="OwnerTitle" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="EditTitle" xml:space="preserve">
    <value>Edición</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>Volver a la lista</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>Para salvaguardar</value>
  </data>
  <data name="Lang" xml:space="preserve">
    <value>fría</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Idioma</value>
  </data>
  <data name="LangCode" xml:space="preserve">
    <value>Fría</value>
  </data>
  <data name="LangDesc" xml:space="preserve">
    <value>Español</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>Ancho</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Regiones</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>Crear una entrada</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Código</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Título</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="MapZoom" xml:space="preserve">
    <value>Zoom de la tarjeta</value>
  </data>
  <data name="MapCenterY" xml:space="preserve">
    <value>Y tarjeta</value>
  </data>
  <data name="MapCenterX" xml:space="preserve">
    <value>X Centro de tarjetas</value>
  </data>
  <data name="RegionsTitleDesc" xml:space="preserve">
    <value>Región en la aplicación móvil</value>
  </data>
  <data name="TitleDetails" xml:space="preserve">
    <value>Cepa</value>
  </data>
  <data name="CreateTitle" xml:space="preserve">
    <value>Nueva entrada</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Agregar</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>¿Estás seguro de que quieres borrar esta entrada?</value>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="DividerOr" xml:space="preserve">
    <value>O</value>
  </data>
  <data name="AddRegion" xml:space="preserve">
    <value>Agregar una región</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Agregar</value>
  </data>
  <data name="YourSecCode" xml:space="preserve">
    <value>Su código de seguridad:</value>
  </data>
  <data name="EmailFrom" xml:space="preserve">
    <value>Art of Foto del panel de control</value>
  </data>
  <data name="EmailCreateAccSubject" xml:space="preserve">
    <value>Art of Foto: confirme la creación de la cuenta</value>
  </data>
  <data name="EmailCreateAccBody" xml:space="preserve">
    <value>Art of Foto recibió una solicitud para crear una cuenta de usuario &lt;br&gt;
Usando su dirección de correo electrónico ({0}). &lt;br&gt;
&lt;br&gt;
Para continuar creando una cuenta utilizando esta dirección de correo electrónico, visite &lt;br&gt;
Siguiente enlace: &lt;br&gt;
&lt;br&gt;
&lt;a href = "{1}" target = "blank" rel = "noopener"&gt; {1} &lt;/a&gt; &lt;br&gt;
&lt;br&gt;
Si no desea crear una cuenta, o si esta solicitud se ha realizado &lt;br&gt;
Por error, simplemente puede ignorar este mensaje. &lt;br&gt;
&lt;br&gt;
Si el enlace de arriba no funciona, o si tiene otros problemas &lt;br&gt;
Con respecto a su cuenta, comuníquese con la administración &lt;br&gt;
en <EMAIL>. &lt;br&gt;
&lt;br&gt;</value>
  </data>
  <data name="AccCreationTitle" xml:space="preserve">
    <value>Creación de cuenta</value>
  </data>
  <data name="AccCeationConfirmEmail" xml:space="preserve">
    <value>Le enviamos un correo electrónico a la siguiente dirección: {0}.
Siga las instrucciones en este correo electrónico para finalizar la creación de su cuenta.</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Región</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>El momento en ese momento</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Acción</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>Parámetros</value>
  </data>
  <data name="ImageURL" xml:space="preserve">
    <value>Imagen (URL)</value>
  </data>
  <data name="Author" xml:space="preserve">
    <value>Autor</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>Redacción por</value>
  </data>
  <data name="EditedTime" xml:space="preserve">
    <value>Última redacción</value>
  </data>
  <data name="ImageHeight" xml:space="preserve">
    <value>Altura de la imagen</value>
  </data>
  <data name="ImageWidth" xml:space="preserve">
    <value>Ancho de la imagen</value>
  </data>
  <data name="ThankYouForConfirmingYourEmailPlease" xml:space="preserve">
    <value>Gracias por confirmar su correo electrónico. Por favor</value>
  </data>
  <data name="ClickHereToLogIn" xml:space="preserve">
    <value>Haga clic aquí para identificarse</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Registro</value>
  </data>
  <data name="YouVeSuccessfullyAuthenticatedWith" xml:space="preserve">
    <value>Te has autenticado con éxito con</value>
  </data>
  <data name="PleaseEnterAUserNameForThisSiteBelow" xml:space="preserve">
    <value>Desea ingresar un nombre de usuario para el sitio y haga clic en el botón para registrarse para finalizar la grabación.</value>
  </data>
  <data name="RegisterTitle" xml:space="preserve">
    <value>Registro</value>
  </data>
  <data name="AssociateYourAccount" xml:space="preserve">
    <value>Combine su cuenta {0}.</value>
  </data>
  <data name="UnsuccessfulLoginWithService" xml:space="preserve">
    <value>Conexión sin éxito con el servicio.</value>
  </data>
  <data name="LoginFailure" xml:space="preserve">
    <value>Error de autenticación</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>Conectar</value>
  </data>
  <data name="OrUseAnotherServiceToLogIn" xml:space="preserve">
    <value>O use un servicio externo para conectar</value>
  </data>
  <data name="UseALocalAccountToLogIn" xml:space="preserve">
    <value>Use una cuenta local</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>Acuérdate de mí</value>
  </data>
  <data name="BtnLogIn" xml:space="preserve">
    <value>Ingresar</value>
  </data>
  <data name="RegisterAsANewUser" xml:space="preserve">
    <value>Crear una cuenta</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Contraseña</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Correo electrónico</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>Confirmar la contraseña</value>
  </data>
  <data name="CreateANewAccount" xml:space="preserve">
    <value>Crear una nueva cuenta</value>
  </data>
  <data name="BtnRegister" xml:space="preserve">
    <value>Registro</value>
  </data>
  <data name="ToolbarLogin" xml:space="preserve">
    <value>Conectar</value>
  </data>
  <data name="ToolbarRegister" xml:space="preserve">
    <value>Registro</value>
  </data>
  <data name="ToolbarHello" xml:space="preserve">
    <value>Buen día,</value>
  </data>
  <data name="ToolbarLogoff" xml:space="preserve">
    <value>(No olvides) desconectar</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="MoreInfo" xml:space="preserve">
    <value>Información</value>
  </data>
  <data name="OnMap" xml:space="preserve">
    <value>En la tarjeta</value>
  </data>
  <data name="Centers" xml:space="preserve">
    <value>Centros</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notas</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Sitio web</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Semejante</value>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>Correo</value>
  </data>
  <data name="Metro" xml:space="preserve">
    <value>Metro</value>
  </data>
  <data name="ExportedBy" xml:space="preserve">
    <value>Exportado por</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Activo</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactivo</value>
  </data>
  <data name="ExportedTime" xml:space="preserve">
    <value>Exportado</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>Subtitular</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Ciudad</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>País</value>
  </data>
  <data name="Uploads" xml:space="preserve">
    <value>Archivos</value>
  </data>
  <data name="UploadImage" xml:space="preserve">
    <value>Descargar una imagen</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>Búsqueda de nombres</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>Exportar</value>
  </data>
  <data name="NotAllowed" xml:space="preserve">
    <value>No autorizado</value>
  </data>
  <data name="Allowed" xml:space="preserve">
    <value>Autorizado</value>
  </data>
  <data name="Needed" xml:space="preserve">
    <value>, necesario !</value>
  </data>
  <data name="ToBeExported" xml:space="preserve">
    <value>A la exportación</value>
  </data>
  <data name="HelpAllowToBeExportedForMobileAppOrNot" xml:space="preserve">
    <value>Desea exportar este registro a la aplicación móvil</value>
  </data>
  <data name="SortList" xml:space="preserve">
    <value>Clasificar</value>
  </data>
  <data name="SortAbc" xml:space="preserve">
    <value>Clasificación ABC</value>
  </data>
  <data name="SortDate" xml:space="preserve">
    <value>Por fecha de cambio</value>
  </data>
  <data name="NewsController_Create_ERRORUImageURLNotValid" xml:space="preserve">
    <value>Error: ¡URL de la imagen incorrecta!</value>
  </data>
  <data name="OwnerTitleShort" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="AppoMobiControlPanel" xml:space="preserve">
    <value>Panel de control</value>
  </data>
  <data name="Exports" xml:space="preserve">
    <value>Exportaciones</value>
  </data>
  <data name="CreateExportFor" xml:space="preserve">
    <value>Exportar:</value>
  </data>
  <data name="ExportType" xml:space="preserve">
    <value>Tipo de exportación</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>Error de acceso</value>
  </data>
  <data name="DonTHaveTheRights" xml:space="preserve">
    <value>Parece que no se le permite acceder a esta sección. Póngase en contacto con el soporte si cree que este es un error.</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>Exportar</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>¿Estás seguro de que quieres exportar?</value>
  </data>
  <data name="ExportsController_Index_ExportComplete" xml:space="preserve">
    <value>Exportación realizada con éxito!</value>
  </data>
  <data name="BaseURL" xml:space="preserve">
    <value>URL básica</value>
  </data>
  <data name="SalonList" xml:space="preserve">
    <value>Lista de centros</value>
  </data>
  <data name="InSection" xml:space="preserve">
    <value>en la sección</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Todo</value>
  </data>
  <data name="ShouldNotBeExported" xml:space="preserve">
    <value>No debe ser exportado</value>
  </data>
  <data name="WasWellExported" xml:space="preserve">
    <value>Ha sido exportado</value>
  </data>
  <data name="ShouldBeExported" xml:space="preserve">
    <value>Para ser exportado</value>
  </data>
  <data name="InRegion" xml:space="preserve">
    <value>en región de</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>por</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Productos</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>Categorías</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>Sistema</value>
  </data>
  <data name="RoleSuperuser" xml:space="preserve">
    <value>Dueño</value>
  </data>
  <data name="RoleAdmin" xml:space="preserve">
    <value>Administrador</value>
  </data>
  <data name="RoleEditor" xml:space="preserve">
    <value>Editor</value>
  </data>
  <data name="RoleNoRole" xml:space="preserve">
    <value>Visitante</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>Padre</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Prioridad</value>
  </data>
  <data name="SortDefault" xml:space="preserve">
    <value>Por defecto</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Información</value>
  </data>
  <data name="Subcategories" xml:space="preserve">
    <value>Subcategorías</value>
  </data>
  <data name="ParentElementToInsertCategoryInto" xml:space="preserve">
    <value>Categoría-padres, en la que se debe insertar el elemento actual</value>
  </data>
  <data name="RootCategory" xml:space="preserve">
    <value>Categoría básica</value>
  </data>
  <data name="CatNewsSlider" xml:space="preserve">
    <value>Baniere de noticias</value>
  </data>
  <data name="CatSecRoot" xml:space="preserve">
    <value>Categoría básica secundaria</value>
  </data>
  <data name="UploadMiniImage" xml:space="preserve">
    <value>Descargar una mini imagen</value>
  </data>
  <data name="ImageURLForMini" xml:space="preserve">
    <value>Mini URL de imagen</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Categoría</value>
  </data>
  <data name="Volume" xml:space="preserve">
    <value>Volumen</value>
  </data>
  <data name="Recommendation" xml:space="preserve">
    <value>Consejo de Thalion</value>
  </data>
  <data name="ILike" xml:space="preserve">
    <value>Me gusta</value>
  </data>
  <data name="Units" xml:space="preserve">
    <value>Unidad</value>
  </data>
  <data name="Keywords" xml:space="preserve">
    <value>Palabras clave</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>Nuevo</value>
  </data>
  <data name="ShowList" xml:space="preserve">
    <value>Mostrar</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Investigación..</value>
  </data>
  <data name="ProductsController_CreateDropdownList_ANYCAT" xml:space="preserve">
    <value>Todas las categorías</value>
  </data>
  <data name="SortCode" xml:space="preserve">
    <value>Por código</value>
  </data>
  <data name="SortCat" xml:space="preserve">
    <value>Por categoría</value>
  </data>
  <data name="RoleMerchandiser" xml:space="preserve">
    <value>Comerciante</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Reiniciar</value>
  </data>
  <data name="CatRoot2" xml:space="preserve">
    <value>Categoría 2 básica</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>Hacer la razón</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>Imagen del anonce</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>Cuerpo</value>
  </data>
  <data name="Face" xml:space="preserve">
    <value>Rostro</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Sí</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="OurChoice" xml:space="preserve">
    <value>Nuestra selección</value>
  </data>
  <data name="ForgotYourPassword" xml:space="preserve">
    <value>¿Has olvidado tu contraseña?</value>
  </data>
  <data name="NoRUTranslation" xml:space="preserve">
    <value>Traducción ausente de RU</value>
  </data>
  <data name="ErrorNotFound" xml:space="preserve">
    <value>Error no encontrado</value>
  </data>
  <data name="ErrorUnknown" xml:space="preserve">
    <value>Error desconocido</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>Enlaces de Internet</value>
  </data>
  <data name="Redirect" xml:space="preserve">
    <value>Enlace</value>
  </data>
  <data name="Clicks" xml:space="preserve">
    <value>Haga clic en Total</value>
  </data>
  <data name="AreYouSureToDelete" xml:space="preserve">
    <value>¿Estás seguro de que quieres borrar?</value>
  </data>
  <data name="Treatment" xml:space="preserve">
    <value>Tratamiento</value>
  </data>
  <data name="Treatments" xml:space="preserve">
    <value>Tratos</value>
  </data>
  <data name="ErrorPleaseCheckRequirementsForFieldsBelow" xml:space="preserve">
    <value>Error: verifique los requisitos de los campos a continuación.</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>Productos</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>Gestión</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Usuarios</value>
  </data>
  <data name="ResetChanges" xml:space="preserve">
    <value>Cancelar el cambio</value>
  </data>
  <data name="DoNotSave" xml:space="preserve">
    <value>Escotado por detrás</value>
  </data>
  <data name="List" xml:space="preserve">
    <value>Lista</value>
  </data>
  <data name="EditorSNotesInternalUseOnly" xml:space="preserve">
    <value>Notas del editor, solo uso interno</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ControlPanelHtml" xml:space="preserve">
    <value>&lt;strong&gt; Art del panel de control Foto &lt;/strong&gt;</value>
  </data>
  <data name="RememberMe2" xml:space="preserve">
    <value>¿Me puse?</value>
  </data>
  <data name="DidYouRememberYourPassword" xml:space="preserve">
    <value>Contraseña encontrada?</value>
  </data>
  <data name="BtnResetPassword" xml:space="preserve">
    <value>Restablecer contraseña</value>
  </data>
  <data name="PleaseCheckYourEmailToResetYourPassword" xml:space="preserve">
    <value>Consulte su correo electrónico para restablecer su contraseña.</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Has olvidado tu contraseña</value>
  </data>
  <data name="DoYouHaveAnAccount" xml:space="preserve">
    <value>Ya grabado?</value>
  </data>
  <data name="RegisterAccount" xml:space="preserve">
    <value>Crear una cuenta</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="AgreeToTerms" xml:space="preserve">
    <value>Acepto &lt;strong&gt; términos &lt;/strong&gt;</value>
  </data>
  <data name="YouMustAcceptTermsAndConditions" xml:space="preserve">
    <value>Debe aceptar los términos y condiciones.</value>
  </data>
  <data name="PasswordAndConfirmationPasswordDoNotMatch" xml:space="preserve">
    <value>La contraseña y la contraseña de confirmación no se corresponden.</value>
  </data>
  <data name="StringLengthError" xml:space="preserve">
    <value>{0} debe ser largo de {2} carácter mínimo.</value>
  </data>
  <data name="BadUsernameOrPassword" xml:space="preserve">
    <value>Nombre de usuario o contraseña no válida.</value>
  </data>
  <data name="EmailAlreadyTaken" xml:space="preserve">
    <value>La dirección de correo electrónico '{0}' ya se usa.</value>
  </data>
  <data name="MailSubjectResetPassword" xml:space="preserve">
    <value>Restablecer la contraseña</value>
  </data>
  <data name="ResetYourPasswordMailBody" xml:space="preserve">
    <value>Restablezca su contraseña haciendo clic en &lt;a href = "{0}"&gt; este enlace &lt;/a&gt;.</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>Nueva contraseña</value>
  </data>
  <data name="PleaseEnterYourNewPasswordBelow" xml:space="preserve">
    <value>Ingrese su nueva contraseña.</value>
  </data>
  <data name="DidYouRememberYourOLDPassword" xml:space="preserve">
    <value>¿Contraseña antigua encontrada?</value>
  </data>
  <data name="AccountController_ResetPassword_InvalidToken" xml:space="preserve">
    <value>Su enlace de reinicio ha expirado.</value>
  </data>
  <data name="YourNewPasswordHasBeenSet" xml:space="preserve">
    <value>Su nueva contraseña ha sido definida.</value>
  </data>
  <data name="Class" xml:space="preserve">
    <value>Clase</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>Privacidad</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>Publicado en</value>
  </data>
  <data name="FastLoginWith" xml:space="preserve">
    <value>Acceso rápido con</value>
  </data>
  <data name="OrEnterYourCredentials" xml:space="preserve">
    <value>o ingrese sus datos</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Parámetros</value>
  </data>
  <data name="ExternalLogins" xml:space="preserve">
    <value>Iniciar sesión con la red social</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>Nean</value>
  </data>
  <data name="ChangeYourPassword" xml:space="preserve">
    <value>Cambiar la contraseña</value>
  </data>
  <data name="ManageAccount" xml:space="preserve">
    <value>Administre su cuenta</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>Para cambiar</value>
  </data>
  <data name="TwoFactorAuthentication" xml:space="preserve">
    <value>Autenticación de dos factores</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Tu tal como.</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>Desactivado</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>Activo</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Permitir</value>
  </data>
  <data name="Manage" xml:space="preserve">
    <value>Administrar</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Desactivar</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>BORRAR</value>
  </data>
  <data name="ManageYourExternalLogins" xml:space="preserve">
    <value>Administre sus inicios de sesión externos</value>
  </data>
  <data name="X_Theme" xml:space="preserve">
    <value>Piel</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Llave</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Cerca</value>
  </data>
  <data name="EmailConfirmed" xml:space="preserve">
    <value>Gracias por confirmar su correo electrónico. Ahora puede usar su información de identificación para conectarse.</value>
  </data>
  <data name="AccNotActiveForCLient" xml:space="preserve">
    <value>Su cuenta aún no ha sido otorgada a un cliente de arte existente del cliente FOTO. Póngase en contacto con su asistencia técnica.</value>
  </data>
  <data name="NothingWasFound" xml:space="preserve">
    <value>¡No se encontró nada!</value>
  </data>
  <data name="ClientControlPanel" xml:space="preserve">
    <value>Panel de control del cliente</value>
  </data>
  <data name="ThankYouForBeingPatient" xml:space="preserve">
    <value>Gracias por ser paciente. Estamos trabajando en el sitio que pronto volverá.</value>
  </data>
  <data name="UnderConstruction" xml:space="preserve">
    <value>Bajo construcción</value>
  </data>
  <data name="SorryWeReDoingSomeWorkOnTheSite" xml:space="preserve">
    <value>Lo siento, trabajamos en el sitio</value>
  </data>
  <data name="Desktop" xml:space="preserve">
    <value>De oficina</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Contiene</value>
  </data>
  <data name="ProductElements" xml:space="preserve">
    <value>Elementos de productos</value>
  </data>
  <data name="Specifications" xml:space="preserve">
    <value>Componentes</value>
  </data>
  <data name="DescriptionRU" xml:space="preserve">
    <value>Descripción ru</value>
  </data>
  <data name="DescriptionEN" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="DescriptionFR" xml:space="preserve">
    <value>Descripción fr</value>
  </data>
  <data name="DisabledEntryDesc" xml:space="preserve">
    <value>No se exportará fuera del panel de control si es así</value>
  </data>
  <data name="UploadFileFieldDesc" xml:space="preserve">
    <value>Puede descargar un archivo o ingresar una URL existente en el campo siguiente</value>
  </data>
  <data name="NoAction" xml:space="preserve">
    <value>Sin acciones</value>
  </data>
  <data name="OpenProductInApp" xml:space="preserve">
    <value>Abra el producto en la aplicación</value>
  </data>
  <data name="NavigateToUrl" xml:space="preserve">
    <value>Navegar a un enlace web</value>
  </data>
  <data name="FieldMustBeUnique" xml:space="preserve">
    <value>El valor del campo '{0}' debe ser único</value>
  </data>
  <data name="ContentLanguages" xml:space="preserve">
    <value>Lenguajes de contenido</value>
  </data>
  <data name="Enter2LettersLanguageCodes" xml:space="preserve">
    <value>Ingrese los códigos de idioma de 2 letras</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>Desconocido</value>
  </data>
  <data name="CompanyInfo" xml:space="preserve">
    <value>Sobre nosotros</value>
  </data>
  <data name="OrUploadFromDisk" xml:space="preserve">
    <value>Para cambiar ..</value>
  </data>
  <data name="SortOutDate" xml:space="preserve">
    <value>Por fecha de lanzamiento</value>
  </data>
  <data name="SortPriority" xml:space="preserve">
    <value>Prioridad del triaje</value>
  </data>
  <data name="ShowOnPage" xml:space="preserve">
    <value>Por página</value>
  </data>
  <data name="ExportSection" xml:space="preserve">
    <value>Exportación rápida</value>
  </data>
  <data name="PlsConfirmExport" xml:space="preserve">
    <value>¿Estás seguro de que quieres exportar esta sección ahora?</value>
  </data>
  <data name="BaseControllerContent__IndexGet_ExportCompletedWithSuccess" xml:space="preserve">
    <value>La exportación se llevó a cabo con éxito.</value>
  </data>
  <data name="SiteLoading" xml:space="preserve">
    <value>Cargando</value>
  </data>
  <data name="PushMessages" xml:space="preserve">
    <value>Enviar mensajes</value>
  </data>
  <data name="NewsMenu" xml:space="preserve">
    <value>Tus noticias</value>
  </data>
  <data name="NavigateToWww" xml:space="preserve">
    <value>Navegue a un enlace web con el navegador interno</value>
  </data>
  <data name="SimpleMessage" xml:space="preserve">
    <value>Mensaje simple</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>Enviar</value>
  </data>
  <data name="SaveForLater" xml:space="preserve">
    <value>Guardar sin enviar</value>
  </data>
  <data name="PushEngagedUsers" xml:space="preserve">
    <value>Usuarios activos</value>
  </data>
  <data name="PushActiveUsers" xml:space="preserve">
    <value>Recibir usuarios</value>
  </data>
  <data name="PushInactiveUsers" xml:space="preserve">
    <value>Usuarios no activos</value>
  </data>
  <data name="OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage" xml:space="preserve">
    <value>El texto no puede estar vacío para el idioma inglés.</value>
  </data>
  <data name="Android" xml:space="preserve">
    <value>Androide</value>
  </data>
  <data name="AppleIOS" xml:space="preserve">
    <value>Apple iOS</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>Revelador</value>
  </data>
  <data name="DevicesTotal" xml:space="preserve">
    <value>Total de dispositivos</value>
  </data>
  <data name="SavedForLater" xml:space="preserve">
    <value>Respaldo</value>
  </data>
  <data name="maskSentToDevices" xml:space="preserve">
    <value>Enviado a {0} avión</value>
  </data>
  <data name="PushMessagesWereNotConfigured" xml:space="preserve">
    <value>Los mensajes de empuje aún no se han configurado para usted.</value>
  </data>
  <data name="Tenants" xml:space="preserve">
    <value>Contenido</value>
  </data>
  <data name="ClientGlobalSettings" xml:space="preserve">
    <value>Configuración general del cliente</value>
  </data>
  <data name="ChangesSaved" xml:space="preserve">
    <value>Modificaciones registradas</value>
  </data>
  <data name="NewsController_NewsController_ByRegion" xml:space="preserve">
    <value>Por región de visualización</value>
  </data>
  <data name="MessagableDevicesTotal" xml:space="preserve">
    <value>Número total de dispositivos Messagables</value>
  </data>
  <data name="TotalInstallations" xml:space="preserve">
    <value>Número total de instalaciones</value>
  </data>
  <data name="CreateAPassword" xml:space="preserve">
    <value>Crear una contraseña</value>
  </data>
  <data name="YourSecCodeLoginMask" xml:space="preserve">
    <value>Su código de seguridad es {0}. Úselo para ingresar al panel de control del arte de Foto.</value>
  </data>
  <data name="IncorrectEmailAddressOrPhoneNumber" xml:space="preserve">
    <value>Dirección de correo electrónico o número de teléfono incorrecto.</value>
  </data>
  <data name="AUserWithThisPhoneNumberWasNotFoundPleaseRegister" xml:space="preserve">
    <value>No se ha encontrado un usuario con este número de teléfono. Por favor regístrese.</value>
  </data>
  <data name="PleaseCheckYouDevice" xml:space="preserve">
    <value>Por favor revise su teléfono</value>
  </data>
  <data name="WeHaveSentAVerificationCodeToYourNumber" xml:space="preserve">
    <value>Enviamos un código de verificación de números</value>
  </data>
  <data name="UserWithThisPhoneNumberAlreadyRegistered" xml:space="preserve">
    <value>Un usuario con este número de teléfono ya está guardado.</value>
  </data>
  <data name="WrongCodeEntered" xml:space="preserve">
    <value>Código de verificación incorrecto.</value>
  </data>
  <data name="StatusConfirmed" xml:space="preserve">
    <value>Confirmado</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>Dirección</value>
  </data>
  <data name="ThankYou" xml:space="preserve">
    <value>¡Gracias!</value>
  </data>
  <data name="StatusPendingConfirmation" xml:space="preserve">
    <value>Esperando confirmación</value>
  </data>
  <data name="StatusDisapproved" xml:space="preserve">
    <value>Denegado</value>
  </data>
  <data name="BookingSystem" xml:space="preserve">
    <value>Sistema de reservas</value>
  </data>
  <data name="BookingFrontDesk" xml:space="preserve">
    <value>Bienvenido</value>
  </data>
  <data name="BookingSchedule" xml:space="preserve">
    <value>Horarios disponibles</value>
  </data>
  <data name="BookingRequests" xml:space="preserve">
    <value>Solicitudes de reserva</value>
  </data>
  <data name="BookingObjects" xml:space="preserve">
    <value>Objetos de reserva</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>Crear un evento</value>
  </data>
  <data name="InsertEventName" xml:space="preserve">
    <value>Ingrese el nombre del evento</value>
  </data>
  <data name="DragAndDropEventsOnTheCalendar" xml:space="preserve">
    <value>Deslice y coloque un evento en el calendario</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>Error de conexión</value>
  </data>
  <data name="LatestMobileAppVersion" xml:space="preserve">
    <value>Última versión de la aplicación móvil</value>
  </data>
  <data name="OutdatedMobileAppVersion" xml:space="preserve">
    <value>Versión de la aplicación móvil obsoleta</value>
  </data>
  <data name="StartEvent" xml:space="preserve">
    <value>Comienzo</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>Todo el día</value>
  </data>
  <data name="_2Weeks" xml:space="preserve">
    <value>2 semanas</value>
  </data>
  <data name="AppoController_Bookable_BlockDayForBooking" xml:space="preserve">
    <value>Reserva prohibida</value>
  </data>
  <data name="AreYouSureToDeleteThisEvent" xml:space="preserve">
    <value>¿Estás seguro de que quieres eliminar este evento?</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>Un segundo por favor.</value>
  </data>
  <data name="EditEvent" xml:space="preserve">
    <value>Edición de eventos</value>
  </data>
  <data name="EventCard" xml:space="preserve">
    <value>Hojas de eventos</value>
  </data>
  <data name="Confirned" xml:space="preserve">
    <value>confirmado</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>Necesito confirmar</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>Objeto</value>
  </data>
  <data name="ServicesCategories" xml:space="preserve">
    <value>Categorías de servicios</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Servicios</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Servicio</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Cliente</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>Detalles</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Nombre largo</value>
  </data>
  <data name="MapX" xml:space="preserve">
    <value>Mapx</value>
  </data>
  <data name="KeyHint" xml:space="preserve">
    <value>Se requiere una clave única, para su uso en la aplicación móvil.</value>
  </data>
  <data name="ReservedField" xml:space="preserve">
    <value>No se usa actualmente.</value>
  </data>
  <data name="DbNews_UrlProductCodeEtc" xml:space="preserve">
    <value>Enlaces de Internet o código de producto, Centro (campo de "clave única") etc.</value>
  </data>
  <data name="DbNews_WhatToDoWhenNewsFrameIsClickedInApp" xml:space="preserve">
    <value>Acción a tomar en caso de hacer clic en las noticias</value>
  </data>
  <data name="DbNews_NewsText" xml:space="preserve">
    <value>Mensaje de texto</value>
  </data>
  <data name="DbNews_LanguageAreaTheNewsWillBeShownIn" xml:space="preserve">
    <value>Región cultural a lo que las noticias</value>
  </data>
  <data name="DbNews_ImageToBeShownInTheNews" xml:space="preserve">
    <value>Imagen del anuncio de noticias</value>
  </data>
  <data name="InternationalTitlesLanguage" xml:space="preserve">
    <value>Idioma para nombres internacionales</value>
  </data>
  <data name="PriorityDesc" xml:space="preserve">
    <value>Prioridad de posición en caso de visualización en una lista</value>
  </data>
  <data name="EnabledModules" xml:space="preserve">
    <value>Modulados activos</value>
  </data>
  <data name="NeedAllUsersRelog" xml:space="preserve">
    <value>Desconectar a los usuarios</value>
  </data>
  <data name="NeedAllUsersRelogDesc" xml:space="preserve">
    <value>Obligando a todos los usuarios del panel de control relacionado con este cliente para volver a conectarse para que los cambios visuales entren en vigencia.</value>
  </data>
  <data name="HowToUse" xml:space="preserve">
    <value>Utilizar consejos</value>
  </data>
  <data name="RefCodeDesc" xml:space="preserve">
    <value>Código de referencia</value>
  </data>
  <data name="TargetPlatfrom" xml:space="preserve">
    <value>Plataforma</value>
  </data>
  <data name="TitleDesc" xml:space="preserve">
    <value>El título mostrado</value>
  </data>
  <data name="MessageTextDesc" xml:space="preserve">
    <value>El texto del mensaje</value>
  </data>
  <data name="TargetSegment" xml:space="preserve">
    <value>Usuarios</value>
  </data>
  <data name="TenantNameDesc" xml:space="preserve">
    <value>Nombre del cliente en el panel de control</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Precio</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>Días de inauguración</value>
  </data>
  <data name="WorkingTimeStart" xml:space="preserve">
    <value>Edificio abriendo el suyo</value>
  </data>
  <data name="WorkingTimeEnd" xml:space="preserve">
    <value>Apertura final</value>
  </data>
  <data name="SexRestriction" xml:space="preserve">
    <value>Se requiere sexo</value>
  </data>
  <data name="WorkingTimePauseEnd" xml:space="preserve">
    <value>Ruptura</value>
  </data>
  <data name="LandingForClients" xml:space="preserve">
    <value>Clientes</value>
  </data>
  <data name="LandingEnterHere" xml:space="preserve">
    <value>Entra aquí</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Equipos</value>
  </data>
  <data name="Goalkeepers" xml:space="preserve">
    <value>Porteros</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>Entusiastas</value>
  </data>
  <data name="BookingStatus_Confirmed" xml:space="preserve">
    <value>Confirmado</value>
  </data>
  <data name="NameTitle" xml:space="preserve">
    <value>Título</value>
  </data>
  <data name="BookingFrontDeskStatusType_Confirmed" xml:space="preserve">
    <value>Confirmado</value>
  </data>
  <data name="PromoStatus_Open" xml:space="preserve">
    <value>Activo</value>
  </data>
  <data name="SortByStatus" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="MenuPageContacts" xml:space="preserve">
    <value>Contactos</value>
  </data>
  <data name="MenuPageNews" xml:space="preserve">
    <value>Noticias</value>
  </data>
  <data name="PageNewsTitle" xml:space="preserve">
    <value>Noticias</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="ShowOnMap" xml:space="preserve">
    <value>En la tarjeta</value>
  </data>
  <data name="PageContactsInfo" xml:space="preserve">
    <value>Información</value>
  </data>
  <data name="PageSalonList" xml:space="preserve">
    <value>Lista</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="ErrorTitle" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="iSalonList" xml:space="preserve">
    <value>Centros</value>
  </data>
  <data name="X_AboutUs" xml:space="preserve">
    <value>Sobre nosotros</value>
  </data>
  <data name="OnMapSalon" xml:space="preserve">
    <value>En la tarjeta</value>
  </data>
  <data name="MenuProductsShort" xml:space="preserve">
    <value>Productos</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="X_DeveloperShort" xml:space="preserve">
    <value>Revelador</value>
  </data>
  <data name="EndEvent" xml:space="preserve">
    <value>Fin</value>
  </data>
  <data name="Since" xml:space="preserve">
    <value>Desde</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Clasificación</value>
  </data>
  <data name="SortRating" xml:space="preserve">
    <value>Por calificación</value>
  </data>
  <data name="VK" xml:space="preserve">
    <value>Vkontakte</value>
  </data>
  <data name="ArenaFeaturesDesc" xml:space="preserve">
    <value>Información</value>
  </data>
  <data name="DifficultyLevel" xml:space="preserve">
    <value>Nivel de dificultad</value>
  </data>
  <data name="PriceDetailsDesc" xml:space="preserve">
    <value>"por hora", etc.</value>
  </data>
  <data name="PriceDetails" xml:space="preserve">
    <value>Detalles del precio</value>
  </data>
  <data name="WeekDays" xml:space="preserve">
    <value>Días de semana</value>
  </data>
  <data name="GenerateDropDowns_Unknown" xml:space="preserve">
    <value>Desconocido</value>
  </data>
  <data name="WorkingTimePauseStart" xml:space="preserve">
    <value>Empezar</value>
  </data>
  <data name="TimeStart" xml:space="preserve">
    <value>Hora de inicio</value>
  </data>
  <data name="TimeEnd" xml:space="preserve">
    <value>Tiempo de finalización</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Equipo</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>Detalles del evento</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>Eventos</value>
  </data>
  <data name="EventsElements" xml:space="preserve">
    <value>Elementos de eventos</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>Organizaciones</value>
  </data>
  <data name="Organization" xml:space="preserve">
    <value>Organización</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>Tipo de evento</value>
  </data>
  <data name="BaseControllerContent_GenerateDropDowns_Rally" xml:space="preserve">
    <value>Reunión</value>
  </data>
  <data name="Championship" xml:space="preserve">
    <value>Campeonato</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Otro</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>Tipo de horario</value>
  </data>
  <data name="ByDaysOfWeek" xml:space="preserve">
    <value>Por días de semana</value>
  </data>
  <data name="WithFixedDate" xml:space="preserve">
    <value>Con fechas fijas</value>
  </data>
  <data name="ScheduleTypeDesc" xml:space="preserve">
    <value>Si con fechas fijas, no se usan días de la semana y viceversa.</value>
  </data>
  <data name="Schedules" xml:space="preserve">
    <value>Horario</value>
  </data>
  <data name="NeedRelogUser" xml:space="preserve">
    <value>Iniciar sesión en el usuario</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>Fecha de nacimiento</value>
  </data>
  <data name="ValidUsernameRequired" xml:space="preserve">
    <value>Se requiere nombre de usuario válido</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>Horas de trabajo</value>
  </data>
  <data name="BookingStatus_Unknown" xml:space="preserve">
    <value>Desconocido</value>
  </data>
  <data name="BookingStatus_Pending" xml:space="preserve">
    <value>Bookingstatuspending</value>
  </data>
  <data name="BookingStatus_Rejected" xml:space="preserve">
    <value>Reserva</value>
  </data>
  <data name="BookingStatus_Archived" xml:space="preserve">
    <value>Reserva estatusarchived</value>
  </data>
  <data name="BookingRequest" xml:space="preserve">
    <value>Solicitud de reserva</value>
  </data>
  <data name="DaysOfWeek_Sunday" xml:space="preserve">
    <value>Domingo</value>
  </data>
  <data name="DaysOfWeek_Monday" xml:space="preserve">
    <value>Lunes</value>
  </data>
  <data name="DaysOfWeek_Tuesday" xml:space="preserve">
    <value>Martes</value>
  </data>
  <data name="DaysOfWeek_Wednesday" xml:space="preserve">
    <value>Miércoles</value>
  </data>
  <data name="DaysOfWeek_Thursday" xml:space="preserve">
    <value>Jueves</value>
  </data>
  <data name="DaysOfWeek_Friday" xml:space="preserve">
    <value>Viernes</value>
  </data>
  <data name="DaysOfWeek_Saturday" xml:space="preserve">
    <value>Sábado</value>
  </data>
  <data name="WorkingTimeDetailed" xml:space="preserve">
    <value>Horas de trabajo detalladas</value>
  </data>
  <data name="AppoConfirmAuto" xml:space="preserve">
    <value>Reservas de autoconfirm</value>
  </data>
  <data name="AppoConfirmAutoDesc" xml:space="preserve">
    <value>auto/manual</value>
  </data>
  <data name="AppoExplicitBookable" xml:space="preserve">
    <value>Tiempo de reserva explícito</value>
  </data>
  <data name="AppoExplicitBookableDesc" xml:space="preserve">
    <value>Si el tiempo de reserva debe indicarse en el tiempo de reserva para que cada objeto esté disponible</value>
  </data>
  <data name="btnBook" xml:space="preserve">
    <value>Libro</value>
  </data>
  <data name="Gallery" xml:space="preserve">
    <value>Galería</value>
  </data>
  <data name="YourName" xml:space="preserve">
    <value>Su nombre</value>
  </data>
  <data name="BtnBookNow" xml:space="preserve">
    <value>¡Libro ahora!</value>
  </data>
  <data name="BookOnline" xml:space="preserve">
    <value>Libro en línea</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Volver</value>
  </data>
  <data name="YourFName" xml:space="preserve">
    <value>Nombre de pila</value>
  </data>
  <data name="YourLName" xml:space="preserve">
    <value>Apellido</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>Cadena</value>
  </data>
  <data name="UpdatingData" xml:space="preserve">
    <value>Actualización de datos ..</value>
  </data>
  <data name="AppoNoTimeDesc" xml:space="preserve">
    <value>Para las condiciones dadas no hay tiempo disponible. Intente cambiar las condiciones a continuación:</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>¡Ups!</value>
  </data>
  <data name="Canceled" xml:space="preserve">
    <value>Cancelado</value>
  </data>
  <data name="ClientId" xml:space="preserve">
    <value>ID de cliente</value>
  </data>
  <data name="AppoTimeDescWho" xml:space="preserve">
    <value>{0} te estará esperando en {1}</value>
  </data>
  <data name="BookingDateTimeDescFormat" xml:space="preserve">
    <value>Te estaremos esperando en {0}</value>
  </data>
  <data name="AppoTimeDescPending" xml:space="preserve">
    <value>Por favor espera la confirmación de {0}</value>
  </data>
  <data name="ConfirmationPendingTitle" xml:space="preserve">
    <value>Confirmación pendiente</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>Imagen</value>
  </data>
  <data name="PatternUrl" xml:space="preserve">
    <value>Patrón</value>
  </data>
  <data name="WallpaperUrl" xml:space="preserve">
    <value>Papel tapiz</value>
  </data>
  <data name="ControlPanel" xml:space="preserve">
    <value>Panel de control</value>
  </data>
  <data name="AppStrings" xml:space="preserve">
    <value>Instrumentos de cuerda</value>
  </data>
  <data name="TweakApp" xml:space="preserve">
    <value>Ajustar móvil</value>
  </data>
  <data name="NoTimeAvailable" xml:space="preserve">
    <value>No hay tiempo disponible</value>
  </data>
  <data name="ForBookingOnly" xml:space="preserve">
    <value>Solo para reservar</value>
  </data>
  <data name="Sections" xml:space="preserve">
    <value>Secciones</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>Artículo</value>
  </data>
  <data name="SeeAlso" xml:space="preserve">
    <value>Ver también:</value>
  </data>
  <data name="PriceMask" xml:space="preserve">
    <value>Máscara de precio</value>
  </data>
  <data name="Appearence" xml:space="preserve">
    <value>Aparición</value>
  </data>
  <data name="SortNotes" xml:space="preserve">
    <value>Por notas</value>
  </data>
  <data name="OurContacts" xml:space="preserve">
    <value>Contáctenos</value>
  </data>
  <data name="HowToGet" xml:space="preserve">
    <value>Encontrar ruta</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>Volver</value>
  </data>
  <data name="BookingObjectsShort" xml:space="preserve">
    <value>Objetos</value>
  </data>
  <data name="ExplainDate_Today" xml:space="preserve">
    <value>Hoy</value>
  </data>
  <data name="ExplainDate_Tomm" xml:space="preserve">
    <value>Mañana</value>
  </data>
  <data name="ExplainDate_X" xml:space="preserve">
    <value>En {0} días</value>
  </data>
  <data name="ExplainDate_X1" xml:space="preserve">
    <value>En {0} días</value>
  </data>
  <data name="ExplainDate_X2" xml:space="preserve">
    <value>En {0} días</value>
  </data>
  <data name="Authenticating" xml:space="preserve">
    <value>Autenticación ..</value>
  </data>
  <data name="YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins" xml:space="preserve">
    <value>Has intentado demasiadas veces, intente nuevamente en {0} mins.</value>
  </data>
  <data name="RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater" xml:space="preserve">
    <value>El registro falló. Verifique que haya proporcionado un número de teléfono válido o vuelva a intentarlo más tarde.</value>
  </data>
  <data name="ПроверьтеКорректностьВведенныхДанных" xml:space="preserve">
    <value>Verifique que los datos que ingresó son válidos.</value>
  </data>
  <data name="BookingFailed" xml:space="preserve">
    <value>La reserva falló.</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>Verificación del código ..</value>
  </data>
  <data name="WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking" xml:space="preserve">
    <value>Le hemos enviado un código de confirmación por SMS. Introduzca a continuación para procesar su reserva:</value>
  </data>
  <data name="BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry" xml:space="preserve">
    <value>La reserva falló. Tal vez alguien ya se haya tomado ese tiempo, vuelva a intentarlo.</value>
  </data>
  <data name="FailedToVerifyCode" xml:space="preserve">
    <value>No se pudo verificar el código.</value>
  </data>
  <data name="ReloadingBookingData" xml:space="preserve">
    <value>Recarga de datos de reserva.</value>
  </data>
  <data name="BookingDateTimeDesc" xml:space="preserve">
    <value>{0} en {1}</value>
  </data>
  <data name="CodeFromSMS" xml:space="preserve">
    <value>Código de SMS</value>
  </data>
  <data name="BookingFrontDeskStatusType_Canceled" xml:space="preserve">
    <value>Cancelado</value>
  </data>
  <data name="BookingFrontDeskStatusType_Pending" xml:space="preserve">
    <value>Pendiente</value>
  </data>
  <data name="Settings_SelectLanguage" xml:space="preserve">
    <value>Selección de idiomas</value>
  </data>
  <data name="ClickToUploadOrDropFileHere" xml:space="preserve">
    <value>Haga clic para cargar o soltar el archivo aquí.</value>
  </data>
  <data name="LoadingOriginalImage" xml:space="preserve">
    <value>Cargando imagen original ..</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>Vista</value>
  </data>
  <data name="WithoutDescription" xml:space="preserve">
    <value>Sin descripción.</value>
  </data>
  <data name="Galleries" xml:space="preserve">
    <value>Galerías</value>
  </data>
  <data name="SystemNameHint" xml:space="preserve">
    <value>No visible en la aplicación móvil, nombre del sistema utilizado para seleccionar este elemento en las listas, etc.</value>
  </data>
  <data name="ExplainDateWithInterval" xml:space="preserve">
    <value>Te esperamos {0}</value>
  </data>
  <data name="BookingTimeDescAt" xml:space="preserve">
    <value>En {0}</value>
  </data>
  <data name="Blog" xml:space="preserve">
    <value>Blog</value>
  </data>
  <data name="OpenBlogArticle" xml:space="preserve">
    <value>Artículo de blog abierto</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>Fecha de lanzamiento</value>
  </data>
  <data name="SplashLogo" xml:space="preserve">
    <value>Logotipo de salpicadura</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>Imagen de la empresa</value>
  </data>
  <data name="DisplayedOverOurContacts" xml:space="preserve">
    <value>Mostrado sobre nuestros contactos</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>Pregunta</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>Nivel</value>
  </data>
  <data name="QuizzQuestionLevel_Easy" xml:space="preserve">
    <value>Fácil</value>
  </data>
  <data name="QuizzQuestionLevel_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="QuizzQuestionLevel_Hard" xml:space="preserve">
    <value>Duro</value>
  </data>
  <data name="QuizzQuestionLevel_Superhard" xml:space="preserve">
    <value>Sobrepasivo</value>
  </data>
  <data name="QuizzQuestionImageType_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="QuizzQuestionImageType_Avatar" xml:space="preserve">
    <value>Avatar</value>
  </data>
  <data name="Answers" xml:space="preserve">
    <value>Respuestas</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>Respuesta</value>
  </data>
  <data name="Correct" xml:space="preserve">
    <value>Correcto</value>
  </data>
  <data name="QuizzQuestions" xml:space="preserve">
    <value>Preguntas de cuestiones</value>
  </data>
  <data name="SortByLevel" xml:space="preserve">
    <value>Por nivel</value>
  </data>
  <data name="QRCodeImageUrl" xml:space="preserve">
    <value>URL de imagen de código QR</value>
  </data>
  <data name="Quizz" xml:space="preserve">
    <value>Cuestionar</value>
  </data>
  <data name="QuestionDurationTime" xml:space="preserve">
    <value>Tiempo para una pregunta</value>
  </data>
  <data name="Quizzes" xml:space="preserve">
    <value>Cuestionarios</value>
  </data>
  <data name="QuestionDurationTimeSecs" xml:space="preserve">
    <value>Tiempo para todas las preguntas en Secs</value>
  </data>
  <data name="Brands" xml:space="preserve">
    <value>Marcas</value>
  </data>
  <data name="PromoActons" xml:space="preserve">
    <value>Actones promocionales</value>
  </data>
  <data name="IncludeQuestionsWithTags" xml:space="preserve">
    <value>Incluir con etiquetas</value>
  </data>
  <data name="ExcludeQuestionsWithTags" xml:space="preserve">
    <value>Excluir con etiquetas</value>
  </data>
  <data name="SearchKeywords" xml:space="preserve">
    <value>Palabras clave de búsqueda</value>
  </data>
  <data name="PleaseSaveThisRecordToBeAbleToAddSubRecords" xml:space="preserve">
    <value>Guarde este registro para poder agregar subcontrats.</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Marca</value>
  </data>
  <data name="PromoPrizes" xml:space="preserve">
    <value>Premio</value>
  </data>
  <data name="CorrectAnswersPercent" xml:space="preserve">
    <value>Respuestas correctas por ciento</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>Descuento</value>
  </data>
  <data name="PromoAction" xml:space="preserve">
    <value>Acción promocional</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="PromoStatus_Closed" xml:space="preserve">
    <value>Cerrado</value>
  </data>
  <data name="PromoStatus_Incoming" xml:space="preserve">
    <value>Entrante</value>
  </data>
  <data name="PromoStatus_Other" xml:space="preserve">
    <value>Otro</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>Salida</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Cargando...</value>
  </data>
  <data name="Success_" xml:space="preserve">
    <value>Éxito</value>
  </data>
  <data name="CouponPercent" xml:space="preserve">
    <value>Porcentaje de cupón</value>
  </data>
  <data name="LinkMoreInfo" xml:space="preserve">
    <value>Más enlace de información</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Nombre de pila</value>
  </data>
  <data name="QuestionsTotal" xml:space="preserve">
    <value>Preguntas totales para mostrar</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Importar</value>
  </data>
  <data name="IncludeQuestionsWithTagsDesc" xml:space="preserve">
    <value>* - Incluya todas las preguntas. También puede especificar otras etiquetas personalizadas que se incluirán.</value>
  </data>
  <data name="OpenPromoInApp" xml:space="preserve">
    <value>Abra la promoción en la aplicación</value>
  </data>
  <data name="MaxPrizes" xml:space="preserve">
    <value>Premios Total</value>
  </data>
  <data name="PrizesLeft" xml:space="preserve">
    <value>Los premios quedan</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Perfil</value>
  </data>
  <data name="CustomerConnectResult_Pending" xml:space="preserve">
    <value>Pendiente</value>
  </data>
  <data name="CustomerConnectResult_Approved" xml:space="preserve">
    <value>Aprobado</value>
  </data>
  <data name="CustomerConnectResult_Denied" xml:space="preserve">
    <value>Denegado</value>
  </data>
  <data name="CustomerConnectResult_NetworkError" xml:space="preserve">
    <value>Error de red</value>
  </data>
  <data name="CustomerConnectResult_UnknownError" xml:space="preserve">
    <value>Error desconocido</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Usuario</value>
  </data>
  <data name="TotalConns" xml:space="preserve">
    <value>Solicitudes totales</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Pedido</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>Solicitudes</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Olor</value>
  </data>
  <data name="TotalConnsOk" xml:space="preserve">
    <value>Solicitudes confirmadas</value>
  </data>
  <data name="CustomerConnectResult_Used" xml:space="preserve">
    <value>Venció</value>
  </data>
  <data name="TimeCalculator_Sec" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="TimeCalculator_Min" xml:space="preserve">
    <value>m</value>
  </data>
  <data name="TimeCalculator_Hour" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="UnitsDescMm" xml:space="preserve">
    <value>Milímetros</value>
  </data>
  <data name="UnitsDescInches" xml:space="preserve">
    <value>Pulgadas</value>
  </data>
  <data name="UnitsKeyMm" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="UnitsKeyInches" xml:space="preserve">
    <value>en</value>
  </data>
  <data name="ChooseUnits" xml:space="preserve">
    <value>Elija unidades</value>
  </data>
  <data name="MenuPageAbout" xml:space="preserve">
    <value>Acerca de</value>
  </data>
  <data name="MenuPageSalons" xml:space="preserve">
    <value>Encuentra un centro</value>
  </data>
  <data name="PageSalonsTitle" xml:space="preserve">
    <value>Tu salón</value>
  </data>
  <data name="ButtonRegionChange" xml:space="preserve">
    <value>Región de cambio</value>
  </data>
  <data name="ButtonNavigate" xml:space="preserve">
    <value>Encontrar ruta</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>Mi salón</value>
  </data>
  <data name="PageFindSalon" xml:space="preserve">
    <value>Nuestros centros</value>
  </data>
  <data name="ErrorConnRegions" xml:space="preserve">
    <value>Error de conexión. Vuelva a intentarlo más tarde.</value>
  </data>
  <data name="ErrorConnSalons" xml:space="preserve">
    <value>Error de conexión. Vuelva a intentarlo más tarde.</value>
  </data>
  <data name="ErrorConnNews" xml:space="preserve">
    <value>Error de conexión. Vuelva a intentarlo más tarde.</value>
  </data>
  <data name="ErrorConnection" xml:space="preserve">
    <value>Error de conexión. Consulte su conexión a Internet y vuelva a intentarlo.</value>
  </data>
  <data name="PageFavSalon" xml:space="preserve">
    <value>Mi centro favorito</value>
  </data>
  <data name="FavoriteEmpty1" xml:space="preserve">
    <value>¡Bienvenido!</value>
  </data>
  <data name="NeedInternet" xml:space="preserve">
    <value>La carga de datos falló. 
Consulte su conexión a Internet.</value>
  </data>
  <data name="ErrorCannotNavigate" xml:space="preserve">
    <value>Asegúrese de tener una aplicación de mapa instalada.</value>
  </data>
  <data name="BrowseSite" xml:space="preserve">
    <value>Sitio web de navegar</value>
  </data>
  <data name="Call" xml:space="preserve">
    <value>Llamar</value>
  </data>
  <data name="SaveItToFavorites" xml:space="preserve">
    <value>¡Guardar para un acceso rápido!</value>
  </data>
  <data name="ButtonAddToFavs" xml:space="preserve">
    <value>Agregar a los favoritos</value>
  </data>
  <data name="ButtonConnect" xml:space="preserve">
    <value>Volver a conectar</value>
  </data>
  <data name="ButtonHowToGetToUs" xml:space="preserve">
    <value>Instrucciones</value>
  </data>
  <data name="AreYouSureRemoveFromFavs" xml:space="preserve">
    <value>¿Realmente eliminar el centro de los favoritos?</value>
  </data>
  <data name="RemoveFromFavs" xml:space="preserve">
    <value>Eliminar de los favoritos</value>
  </data>
  <data name="FavDescBlabla" xml:space="preserve">
    <value>Ahora puede acceder rápidamente a estos datos centrales desde la sección de favoritos.</value>
  </data>
  <data name="AboutSalon" xml:space="preserve">
    <value>Sobre el centro</value>
  </data>
  <data name="GPSPermissionsNeedOn" xml:space="preserve">
    <value>Necesitaríamos acceso a su posición GPS para poder ayudarnos a encontrarnos. Habilitar el acceso ahora?</value>
  </data>
  <data name="GPSPleaseTurnOn" xml:space="preserve">
    <value>Tu GPS está apagado. Actúe para que podamos ayudarlo.</value>
  </data>
  <data name="HowToGetThereMetroTitle" xml:space="preserve">
    <value>Por metro:</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>Contáctenos</value>
  </data>
  <data name="WeOnMap" xml:space="preserve">
    <value>Mostrar en el mapa</value>
  </data>
  <data name="Settings_Copyright" xml:space="preserve">
    <value>© 2019-2025 Art of Foto y respectivos propietarios de contenido</value>
  </data>
  <data name="GettingGPSCoords" xml:space="preserve">
    <value>Encontrar su ubicación ..</value>
  </data>
  <data name="PageSalonListRegion" xml:space="preserve">
    <value>Región</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>Facebook</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="ButtonProSalons" xml:space="preserve">
    <value>Información para centros</value>
  </data>
  <data name="ButtonProPpl" xml:space="preserve">
    <value>Información para especialistas</value>
  </data>
  <data name="ButtonProPartners" xml:space="preserve">
    <value>Los socios inician sesión aquí</value>
  </data>
  <data name="PageHowToGetThereInstructions" xml:space="preserve">
    <value>Cómo encontrarnos</value>
  </data>
  <data name="FavoriteEmpty2" xml:space="preserve">
    <value>Agregue su centro favorito a esta página para un acceso rápido.</value>
  </data>
  <data name="NavigateTo" xml:space="preserve">
    <value>Encontrar ruta</value>
  </data>
  <data name="FavReplaceConfirm" xml:space="preserve">
    <value>¿Reemplazar el favorito existente con este?</value>
  </data>
  <data name="ToSalonList" xml:space="preserve">
    <value>Ver lista de salón completo</value>
  </data>
  <data name="km" xml:space="preserve">
    <value>km</value>
  </data>
  <data name="ButtonFindYourSalon" xml:space="preserve">
    <value>Encuentra tu salón</value>
  </data>
  <data name="FavGratz" xml:space="preserve">
    <value>¡Felicidades!</value>
  </data>
  <data name="ButtonGotIt" xml:space="preserve">
    <value>Yay genial</value>
  </data>
  <data name="ErrorConSalon" xml:space="preserve">
    <value>Error de conexión. Vuelva a intentarlo más tarde.</value>
  </data>
  <data name="iRegion" xml:space="preserve">
    <value>En la tarjeta</value>
  </data>
  <data name="PageTitleSettings" xml:space="preserve">
    <value>Parámetros</value>
  </data>
  <data name="SettingsInterface" xml:space="preserve">
    <value>Interfaz</value>
  </data>
  <data name="Settings_NoTitlesInTabs" xml:space="preserve">
    <value>Sin menú inferior de texto</value>
  </data>
  <data name="SettingsStartFav" xml:space="preserve">
    <value>Mostrar página mi salón (favorito) en el programa inicio</value>
  </data>
  <data name="MenuPageHome" xml:space="preserve">
    <value>Página principal</value>
  </data>
  <data name="SettingsAnimation" xml:space="preserve">
    <value>Deshabilitar animaciones de fondo para el ahorro de baterías</value>
  </data>
  <data name="BackToSalonList" xml:space="preserve">
    <value>Vuelve a la lista de salones</value>
  </data>
  <data name="SettingsTutorial" xml:space="preserve">
    <value>Mostrar siempre el tutorial de bienvenida en el programa Inicio</value>
  </data>
  <data name="MenuSomeMore" xml:space="preserve">
    <value>Aún más ...</value>
  </data>
  <data name="ShowWelcomeSlides" xml:space="preserve">
    <value>Mostrar diapositivas de bienvenida</value>
  </data>
  <data name="StartUp" xml:space="preserve">
    <value>Empezar ahora</value>
  </data>
  <data name="UpdateNeded" xml:space="preserve">
    <value>Hemos lanzado una actualización, ¡actualice la aplicación!</value>
  </data>
  <data name="Bye" xml:space="preserve">
    <value>¡Nos vemos pronto!</value>
  </data>
  <data name="Settings_SilentPush" xml:space="preserve">
    <value>Los mensajes de empuje permanecen en silencio</value>
  </data>
  <data name="AskHideWelcome" xml:space="preserve">
    <value>¿Quieres ocultar este mensaje de bienvenida?</value>
  </data>
  <data name="Tutorial_1_Find" xml:space="preserve">
    <value>Encontrar</value>
  </data>
  <data name="Tutorial_2_Add" xml:space="preserve">
    <value>Colocar</value>
  </data>
  <data name="Tutorial_4_Follow" xml:space="preserve">
    <value>Seguir</value>
  </data>
  <data name="Tutorial_3_Share_Desc" xml:space="preserve">
    <value>en el icono de sección para volver a su raíz</value>
  </data>
  <data name="WebBack" xml:space="preserve">
    <value>Atrás</value>
  </data>
  <data name="SortKm" xml:space="preserve">
    <value>Ordenar por km</value>
  </data>
  <data name="PageSettings_PageSettings_Version" xml:space="preserve">
    <value>versión</value>
  </data>
  <data name="MenuProducts" xml:space="preserve">
    <value>Catálogo de productos</value>
  </data>
  <data name="SubCatsHere" xml:space="preserve">
    <value>Subcategorías:</value>
  </data>
  <data name="AllProductsHere" xml:space="preserve">
    <value>Todos los productos de la categoría</value>
  </data>
  <data name="Conseil" xml:space="preserve">
    <value>Punta de thalion</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>Resultados de la búsqueda</value>
  </data>
  <data name="TapToRead" xml:space="preserve">
    <value>Toque para leer</value>
  </data>
  <data name="SearchProd" xml:space="preserve">
    <value>Búsqueda de productos</value>
  </data>
  <data name="EnterString" xml:space="preserve">
    <value>Buscar</value>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>CALIENTE</value>
  </data>
  <data name="Tutorial_5_Products" xml:space="preserve">
    <value>Vista</value>
  </data>
  <data name="YouHaveSearched" xml:space="preserve">
    <value>Has buscado</value>
  </data>
  <data name="PleaseEnterMoreCharacters" xml:space="preserve">
    <value>¡Ingrese más personajes!</value>
  </data>
  <data name="SearchSalonLabel" xml:space="preserve">
    <value>Centros de búsqueda</value>
  </data>
  <data name="BtnAppSettings" xml:space="preserve">
    <value>Configuración del sistema</value>
  </data>
  <data name="ButtonLater" xml:space="preserve">
    <value>Más tarde</value>
  </data>
  <data name="NiftyGPS_AlertGPSisOff_TurnGPSOn" xml:space="preserve">
    <value>Encender el GPS</value>
  </data>
  <data name="PageSalonList_SortList2_SortedByDistance" xml:space="preserve">
    <value>Ordenado por distancia</value>
  </data>
  <data name="PageSalonList_SortList1_SortedByAlphabet" xml:space="preserve">
    <value>Ordenado por alfabeto</value>
  </data>
  <data name="SliderAnnounce" xml:space="preserve">
    <value>Productos calientes</value>
  </data>
  <data name="WishListDesc" xml:space="preserve">
    <value>Los productos del catálogo se pueden agregar a la lista de deseos. 
La lista es útil para comprar más tarde con su centro de belleza o para compartir con su cosmetista o amigos.</value>
  </data>
  <data name="WishListTitle" xml:space="preserve">
    <value>Mi lista de deseos</value>
  </data>
  <data name="AboutTheCompany" xml:space="preserve">
    <value>Sobre nosotros</value>
  </data>
  <data name="AskForConfirmationWhenRemovingItemFromWishList" xml:space="preserve">
    <value>Solicite confirmación al eliminar los elementos de las listas</value>
  </data>
  <data name="OtherCategories" xml:space="preserve">
    <value>Otras categorías</value>
  </data>
  <data name="GotoProducts" xml:space="preserve">
    <value>Ir al catálogo</value>
  </data>
  <data name="Поделиться" xml:space="preserve">
    <value>Compartir</value>
  </data>
  <data name="INTHECATEGORY" xml:space="preserve">
    <value>Ir a la categoría</value>
  </data>
  <data name="CardProductFull_SetupCell_Ref" xml:space="preserve">
    <value>Árbitro.</value>
  </data>
  <data name="PageWishList_UpdateFavs_ToCatalogue" xml:space="preserve">
    <value>Para catalogarse</value>
  </data>
  <data name="PageWishList_OnBtnShare_МойСписокЖеланийTHALION" xml:space="preserve">
    <value>Mi lista de deseos</value>
  </data>
  <data name="ClearList" xml:space="preserve">
    <value>Lista clara</value>
  </data>
  <data name="HowToBuyProducts" xml:space="preserve">
    <value>Nuestros productos para uso doméstico se pueden comprar solo en centros certificados por Thalion.</value>
  </data>
  <data name="HowToBuyNotFound" xml:space="preserve">
    <value>Si su centro favorito de Thalion no tiene algunos de los productos que desea en stock, háganoslo saber y lo ayudaremos con la compra.</value>
  </data>
  <data name="WhereToBuy" xml:space="preserve">
    <value>Donde comprar</value>
  </data>
  <data name="ContactUs2" xml:space="preserve">
    <value>Contáctenos</value>
  </data>
  <data name="CardProductFull_Fav_OnDown_ConfirmFavDelete" xml:space="preserve">
    <value>Eliminar el elemento de la lista de deseos?</value>
  </data>
  <data name="PageWishList_OnBtnClearList_ConfirmClearList" xml:space="preserve">
    <value>¿Seguro que borrará su lista de deseos?</value>
  </data>
  <data name="GPSPleaseTurnOniOS" xml:space="preserve">
    <value>Necesitaríamos sus coordenadas GPS para ayudarlo con la posición geográfica.</value>
  </data>
  <data name="NumDesc_Items_Format" xml:space="preserve">
    <value>Tienes {0} {1} en tu lista.</value>
  </data>
  <data name="NumDesc_Items_0" xml:space="preserve">
    <value>elementos</value>
  </data>
  <data name="NumDesc_Items_1" xml:space="preserve">
    <value>artículo</value>
  </data>
  <data name="NumDesc_Items_with1" xml:space="preserve">
    <value>elementos</value>
  </data>
  <data name="NumDesc_Items_with2" xml:space="preserve">
    <value>elementos</value>
  </data>
  <data name="NumDesc_Items_with0" xml:space="preserve">
    <value>elementos</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>Obtenga más información</value>
  </data>
  <data name="ItemAddedToWishList" xml:space="preserve">
    <value>Artículo agregado a la lista de deseos</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Presione hacia atrás una vez más para dejar la aplicación</value>
  </data>
  <data name="CatRoot" xml:space="preserve">
    <value>Ver catálogo</value>
  </data>
  <data name="ToCatRoot" xml:space="preserve">
    <value>Para resumir</value>
  </data>
  <data name="iOSTabsStartup_Setup_WhereToFind" xml:space="preserve">
    <value>Donde encontrar</value>
  </data>
  <data name="PrevCategory" xml:space="preserve">
    <value>Categoría previa</value>
  </data>
  <data name="NextCategory" xml:space="preserve">
    <value>Muéstrame más ..</value>
  </data>
  <data name="SeaAlso" xml:space="preserve">
    <value>Ver también</value>
  </data>
  <data name="BackToCatalog" xml:space="preserve">
    <value>Catálogo de productos</value>
  </data>
  <data name="iOSTabsStartup_Setup_Favorites" xml:space="preserve">
    <value>Favoritos</value>
  </data>
  <data name="iOSTabsStartup_Setup_MyPreferences" xml:space="preserve">
    <value>Mis favoritos</value>
  </data>
  <data name="DoYouWantUsToGPS" xml:space="preserve">
    <value>Si desea encontrar los centros de Thalion más cercanos a usted, sea positivo en la siguiente ventana.</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>Hola</value>
  </data>
  <data name="btnTryAgain" xml:space="preserve">
    <value>Intentar otra vez</value>
  </data>
  <data name="btnCheckSettings" xml:space="preserve">
    <value>Verifique la configuración</value>
  </data>
  <data name="ProcessingYourBooking" xml:space="preserve">
    <value>Procesando tu reserva ..</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>Acerca de..</value>
  </data>
  <data name="X_TimeCalcShort" xml:space="preserve">
    <value>El momento en ese momento</value>
  </data>
  <data name="X_TimeCalcFull" xml:space="preserve">
    <value>Calculadora de tiempo</value>
  </data>
  <data name="X_BellowsShort" xml:space="preserve">
    <value>Fuelle</value>
  </data>
  <data name="X_BellowsFull" xml:space="preserve">
    <value>Extensión de fuelles</value>
  </data>
  <data name="X_FocalLength" xml:space="preserve">
    <value>Longitud focal</value>
  </data>
  <data name="X_BellowsDesc" xml:space="preserve">
    <value>Aquí puede obtener un valor preciso para configurar su f/parar usando una cámara de fuelle</value>
  </data>
  <data name="Millisecs" xml:space="preserve">
    <value>ms</value>
  </data>
  <data name="NumericDoubleDot" xml:space="preserve">
    <value>.</value>
  </data>
  <data name="OfflineCompanyDesc" xml:space="preserve">
    <value>El proyecto Art of Foto se fundó a principios de 2011. La idea detrás de la galería es preservar el patrimonio fotográfico de Rusia y, al mismo tiempo, apoyar la fotografía analógica contemporánea, así como para ayudarlo a desarrollarse, tanto artística como tecnológicamente. Abrimos el arte de la galería Foto, un cuarto oscuro y un estudio fotográfico de gran formato en San Petersburgo en 2015.

La Galería Art of Foto es una colección de fotografías que tienen valor histórico y artístico. Por el momento, tenemos obras producidas por conocidos maestros como Valery Plotnikov, Boris Smelov, John Sexton, Leonid Bogdanov, Valentin Samarin, John Wimberly, Robert Doisneau y otros.

El objetivo principal de la galería es promover fotógrafos rusos talentosos, tanto en casa como en el extranjero. Apoyamos activamente a los fotógrafos en San Petersburgo y Moscú cuyas obras probablemente se agregarán al patrimonio artístico de Rusia. 

Los miembros del Art of Foto Artists 'Union organizan exposiciones anuales de fotografías en blanco y negro impresos a mano en Rusia y Europa con el objetivo de crear una imagen positiva de la fotografía rusa tradicional y contemporánea entre los conocedores y expertos en todo el mundo.</value>
  </data>
  <data name="OfflineCompanyAddress" xml:space="preserve">
    <value>Bolshaya Konyushennaya Street, 1, Sankt-Peterburg, Rusia, 191186</value>
  </data>
  <data name="OfflineMapDesc" xml:space="preserve">
    <value>Bienvenido</value>
  </data>
  <data name="Collapse" xml:space="preserve">
    <value>Colapsar</value>
  </data>
  <data name="Expand" xml:space="preserve">
    <value>Expandir</value>
  </data>
  <data name="HelpCalculator" xml:space="preserve">
    <value>C - Presione una vez para restablecer el valor actual, dos veces para reinicio completo
% - Utilizado en combinación con la operación previamente ingresada.
Por ejemplo: presione + luego % luego ingrese un número decimal.
Resultado: ha agregado los porcentajes que acaban de ingresar al tiempo existente.</value>
  </data>
  <data name="X_BellowsHelp" xml:space="preserve">
    <value>Puede cambiar entre mm y pulgadas haciendo clic en el texto correspondiente en el lado derecho del campo de entrada.</value>
  </data>
  <data name="X_EnableSound" xml:space="preserve">
    <value>Sonido</value>
  </data>
  <data name="X_EnableHoursInput" xml:space="preserve">
    <value>Habilitar la entrada de horas</value>
  </data>
  <data name="X_TimerStartedAt" xml:space="preserve">
    <value>El temporizador comenzó en {0}</value>
  </data>
  <data name="X_TimerFinishedFor" xml:space="preserve">
    <value>Temporizador terminado para {0}</value>
  </data>
  <data name="X_35mmHelp" xml:space="preserve">
    <value>Puede cambiar entre mm y pulgadas haciendo clic en el texto correspondiente en el lado derecho del campo de entrada.</value>
  </data>
  <data name="X_DeveloperHelp" xml:space="preserve">
    <value>Esta es una ayuda del módulo de desarrollador.</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>regiones</value>
  </data>
  <data name="Milliliters" xml:space="preserve">
    <value>ml</value>
  </data>
  <data name="X_DeveloperFull" xml:space="preserve">
    <value>Desarrollador de mezcla</value>
  </data>
  <data name="X_35MmShort" xml:space="preserve">
    <value>35 mm</value>
  </data>
  <data name="X_35mmFull" xml:space="preserve">
    <value>Convertir 35 mm</value>
  </data>
  <data name="X_35mmDesc" xml:space="preserve">
    <value>Aquí puede obtener la distancia focal de su lente expresada en un equivalente de 35 mm.</value>
  </data>
  <data name="X_FrameFormat" xml:space="preserve">
    <value>Formato de marco:</value>
  </data>
  <data name="X_WithinVolume" xml:space="preserve">
    <value>En volumen</value>
  </data>
  <data name="X_FromGiven" xml:space="preserve">
    <value>De dado</value>
  </data>
  <data name="X_ResultMl" xml:space="preserve">
    <value>Resultado (Ml.)</value>
  </data>
  <data name="X_SolutionA" xml:space="preserve">
    <value>Parte A</value>
  </data>
  <data name="X_SolutionB" xml:space="preserve">
    <value>Parte B</value>
  </data>
  <data name="X_Water" xml:space="preserve">
    <value>Agua</value>
  </data>
  <data name="X_DeveloperDescA" xml:space="preserve">
    <value>El cálculo de los componentes del desarrollador, basado en los volúmenes especificados.</value>
  </data>
  <data name="X_DeveloperDescB" xml:space="preserve">
    <value>Selección automática de volúmenes para obtener la cantidad especificada del desarrollador.</value>
  </data>
  <data name="X_35mmResult" xml:space="preserve">
    <value>Convertido</value>
  </data>
  <data name="X_BellowsResult" xml:space="preserve">
    <value>f/parada</value>
  </data>
  <data name="X_BellowsResultDesc" xml:space="preserve">
    <value>Su resultado es {0: 0.00}</value>
  </data>
  <data name="Settings_ChooseYourTabsMinMax" xml:space="preserve">
    <value>Elija sus pestañas ({0}/{1} min {2})</value>
  </data>
  <data name="Settings_FavsTabs" xml:space="preserve">
    <value>Seleccionar pestañas que se muestren</value>
  </data>
  <data name="Settings_SelectTheme" xml:space="preserve">
    <value>Piel</value>
  </data>
  <data name="X_ThemeDark" xml:space="preserve">
    <value>Oscuro</value>
  </data>
  <data name="X_ThemeLight" xml:space="preserve">
    <value>Luz</value>
  </data>
  <data name="X_AboutFooter" xml:space="preserve">
    <value>Aplicación desarrollada por Appomobi</value>
  </data>
  <data name="X_35mmResultDesc" xml:space="preserve">
    <value>K = {0}, diagonal es {1}.

{2}</value>
  </data>
  <data name="X_SolutionResult" xml:space="preserve">
    <value>Solución lista</value>
  </data>
  <data name="AskForRating_Question" xml:space="preserve">
    <value>Disfrutando {0}?</value>
  </data>
  <data name="AskForRating_ThanksForNegative" xml:space="preserve">
    <value>Gracias por sus comentarios, ¡intentaremos mejorar nuestra aplicación!</value>
  </data>
  <data name="AskForRating_GooglePlay" xml:space="preserve">
    <value>Gracias por calificarnos en GooglePlay, ¡estaremos muy agradecidos!</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Requerido</value>
  </data>
  <data name="X_NoFilter" xml:space="preserve">
    <value>Sin filtro</value>
  </data>
  <data name="X_ReciprocityHint" xml:space="preserve">
    <value>Cálculo de la exposición, teniendo en cuenta la influencia del efecto Schwarzschild</value>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>Rebosar</value>
  </data>
  <data name="X_AdjustedTime" xml:space="preserve">
    <value>Exposición corregida</value>
  </data>
  <data name="X_Mins" xml:space="preserve">
    <value>Mínimos</value>
  </data>
  <data name="TimeCalculator_Day" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="FilmNotes_Kodak" xml:space="preserve">
    <value>Datos de 2016
El fabricante recomienda hacer una corrección cuando se desarrolle:
&gt; 2 segundos: -10%
&gt; 50 segundos: -20%
&gt; 20 min: -30%</value>
  </data>
  <data name="TestOne" xml:space="preserve">
    <value>Come algunas cosas</value>
  </data>
  <data name="X_UnknownFormula" xml:space="preserve">
    <value>La fórmula no fue comunicada por la marca ...</value>
  </data>
  <data name="X_DevelopmentUnrecommended" xml:space="preserve">
    <value>El fabricante no recomienda esta duración del desarrollo.</value>
  </data>
  <data name="X_Reciprocity" xml:space="preserve">
    <value>Reciprocidad</value>
  </data>
  <data name="X_ReciprocityHelp" xml:space="preserve">
    <value>ADVERTENCIA

El valor de las enmiendas al disparar con el filtro por el fabricante y el tipo de iluminación promedio. 

Recomienda que pruebe los filtros y la película para obtener un resultado estable y preciso.</value>
  </data>
  <data name="ExplainSeconds_0" xml:space="preserve">
    <value>segundos</value>
  </data>
  <data name="ExplainSeconds_1" xml:space="preserve">
    <value>segundos</value>
  </data>
  <data name="ExplainSeconds_X1" xml:space="preserve">
    <value>segundos</value>
  </data>
  <data name="ExplainSeconds_X2" xml:space="preserve">
    <value>segundos</value>
  </data>
  <data name="ExplainSeconds_X" xml:space="preserve">
    <value>segundos</value>
  </data>
  <data name="Tutorial_3_Share" xml:space="preserve">
    <value>Hacer clic</value>
  </data>
  <data name="X_Secs" xml:space="preserve">
    <value>Secs</value>
  </data>
  <data name="X_OwnFormula" xml:space="preserve">
    <value>Debido a la ausencia de datos del fabricante, utilizamos nuestra propia fórmula</value>
  </data>
  <data name="X_Unneeded" xml:space="preserve">
    <value>Según el productor, la corrección no es necesaria en este ajuste para</value>
  </data>
  <data name="X_OurNews" xml:space="preserve">
    <value>Nuestras noticias</value>
  </data>
  <data name="X_NotesKodak3200" xml:space="preserve">
    <value>Datos de 2002
Según el fabricante, no es necesario hacer ajustes por debajo de un segundo, por más de un segundo usamos nuestra propia fórmula</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Cámara</value>
  </data>
  <data name="CameraHelp" xml:space="preserve">
    <value>La cámara está diseñada para ver los negativos en tiempo real. Puede cambiar el filtro, la cámara y grabar el marco en la galería.</value>
  </data>
  <data name="CameraFull" xml:space="preserve">
    <value>Cámara negativa</value>
  </data>
  <data name="PermissionsError" xml:space="preserve">
    <value>Este módulo no puede funcionar sin autorizaciones. Autorice la aplicación en la configuración del sistema o desinstale la aplicación e instálela de cero para obtener la solicitud de autorizaciones del sistema nuevamente.</value>
  </data>
  <data name="NoPermissions" xml:space="preserve">
    <value>Sin autorización</value>
  </data>
  <data name="Viewfinder" xml:space="preserve">
    <value>Visor</value>
  </data>
  <data name="ViewfinderFull" xml:space="preserve">
    <value>Visor</value>
  </data>
  <data name="Selection" xml:space="preserve">
    <value>Selección</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Aplicar</value>
  </data>
  <data name="LensesFor" xml:space="preserve">
    <value>Objetivos para "{0}"</value>
  </data>
  <data name="ChangeFormat" xml:space="preserve">
    <value>Cambiar</value>
  </data>
  <data name="EditPresets" xml:space="preserve">
    <value>Modificar los presets</value>
  </data>
  <data name="CameraZoomHelp" xml:space="preserve">
    <value>Este módulo está diseñado para la simulación aproximada de la orientación analógica. Puede acercarse a la pantalla con los dedos. Los valores verdes se pueden atrapar.</value>
  </data>
  <data name="Preset" xml:space="preserve">
    <value>Programar</value>
  </data>
  <data name="Films" xml:space="preserve">
    <value>Cine</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>Filtros</value>
  </data>
  <data name="NoLensAdded" xml:space="preserve">
    <value>Sin objetivo agregado</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>Formato</value>
  </data>
  <data name="AddLens" xml:space="preserve">
    <value>Agregar un objetivo (mm)</value>
  </data>
  <data name="OptionScreenOn" xml:space="preserve">
    <value>Pantalla aún encendida</value>
  </data>
  <data name="Adjustment" xml:space="preserve">
    <value>Ajuste</value>
  </data>
  <data name="X_OptionUseGeo" xml:space="preserve">
    <value>¡Fotos de geotag!</value>
  </data>
  <data name="X_NeedMoreForGeo" xml:space="preserve">
    <value>Autorizaciones requeridas para geolocar fotos</value>
  </data>
  <data name="X_OptionSpecialCameraFolder" xml:space="preserve">
    <value>Use el arte de la carpeta Foto</value>
  </data>
  <data name="Reconnect" xml:space="preserve">
    <value>Volver a conectar</value>
  </data>
  <data name="BtnOpen" xml:space="preserve">
    <value>Abierto</value>
  </data>
  <data name="LightPad" xml:space="preserve">
    <value>Tabla de desarrollo</value>
  </data>
  <data name="LightPadShort" xml:space="preserve">
    <value>Desarrollo</value>
  </data>
  <data name="Exposure" xml:space="preserve">
    <value>Exposición</value>
  </data>
  <data name="Aperture" xml:space="preserve">
    <value>Abertura</value>
  </data>
  <data name="Shutter" xml:space="preserve">
    <value>Obturador</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>De nuevo</value>
  </data>
</root>