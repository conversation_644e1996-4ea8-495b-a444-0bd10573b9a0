﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="AppoMobi.TransitionPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    Title=""
    BackgroundColor="{x:Static appoMobi:AppColors.Primary}">


    <Grid
        IsVisible="True"
        VerticalOptions="FillAndExpand">
        <ActivityIndicator
            HorizontalOptions="Center"
            IsRunning="True"
            VerticalOptions="Center" />
        <Label
            BackgroundColor="{x:Static appoMobi:AppColors.LoadingBack}"
            FontSize="12"
            HorizontalOptions="FillAndExpand"
            HorizontalTextAlignment="Center"
            Text="{x:Static resX:ResStrings.Loading}"
            TextColor="{x:Static appoMobi:AppColors.BwHighlight}"
            VerticalOptions="End" />
    </Grid>



</ContentPage>