<?xml version="1.0" encoding="utf-8" ?>
<views:BasePageCodeBehind
    x:Class="Sandbox.MainPageCamera"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:views="clr-namespace:Sandbox.Views" 
    xmlns:camera="clr-namespace:DrawnUi.Camera;assembly=DrawnUi.Maui.Camera"
    BackgroundColor="Black">

    <draw:Canvas
        BackgroundColor="Black"
        Gestures="Enabled"
        RenderingMode = "Accelerated"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaLayout Type="Absolute" HorizontalOptions="Fill" VerticalOptions="Fill">
            
            <!-- Camera Preview -->
            <camera:SkiaCamera
                x:Name="CameraControl"
                HorizontalOptions="Fill"
                VerticalOptions="Fill"
                Aspect="AspectFitFill"
                Facing="Default"
                IsOn="True"
                CaptureSuccess="OnCaptureSuccess"
                CaptureFailed="OnCaptureFailed"
                Zoomed="OnZoomed" />

            <!-- Controls -->
            <draw:SkiaLayout 
                Type="Row" 
                UseCache="ImageComposite"
                HorizontalOptions="Center" 
                VerticalOptions="End"
                Margin="16,0,16,100"
                BackgroundColor="#80000000"
                Spacing="16">

                <draw:SkiaButton
                    UseCache="Image"
                    x:Name="FlashButton"
                    Text="Flash Off"
                    BackgroundColor="DarkGray"
                    TextColor="White"
                    CornerRadius="8"
                    WidthRequest="100"
                    HeightRequest="50"
                    Tapped="OnFlashClicked" />

                <draw:SkiaButton
                    UseCache="Image"
                    x:Name="CaptureButton"
                    Text="Capture"
                    BackgroundColor="Red"
                    TextColor="White"
                    CornerRadius="25"
                    WidthRequest="80"
                    HeightRequest="80"
                    Tapped="OnCaptureClicked" />

                <draw:SkiaButton
                    UseCache="Image"
                    x:Name="SwitchCameraButton"
                    Text="Switch"
                    BackgroundColor="DarkGray"
                    TextColor="White"
                    CornerRadius="8"
                    WidthRequest="100"
                    HeightRequest="50"
                    Tapped="OnSwitchCameraClicked" />

            </draw:SkiaLayout>

            <!-- Brightness Test Button -->
            <draw:SkiaButton
                UseCache="Image"
                x:Name="BrightnessButton"
                Text="Test Brightness"
                BackgroundColor="Blue"
                TextColor="White"
                CornerRadius="8"
                WidthRequest="120"
                HeightRequest="40"
                HorizontalOptions="Center"
                VerticalOptions="End"
                Margin="0,0,0,40"
                Tapped="OnBrightnessTestClicked" />

            <!-- Status -->
            <draw:SkiaLabel
                x:Name="StatusLabel"
                Text="Camera Status: Initializing..."
                TextColor="White"
                FontSize="14"
                HorizontalOptions="Center"
                Padding="8" />

            <draw:SkiaLabelFps
                Margin="0,0,4,24"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>

    </draw:Canvas>

</views:BasePageCodeBehind>
