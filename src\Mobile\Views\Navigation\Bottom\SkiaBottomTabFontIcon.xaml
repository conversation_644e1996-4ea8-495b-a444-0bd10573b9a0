﻿<?xml version="1.0" encoding="utf-8" ?>
<tabs1:IconedTabItem
    x:Class="AppoMobi.Mobile.Views.Navigation.SkiaBottomTabFontIcon"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://xamarin.com/schemas/2014/forms/design"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:tabs1="clr-namespace:AppoMobi.Framework.Maui.Controls.Navigation.Tabs;assembly=AppoMobi.Framework.Maui"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="ThisBottomTab"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="FillAndExpand"
    mc:Ignorable="d">

    <!--  touch:TouchEffect.HandlerTapped="{Binding Source={x:Reference ThisBottomTab}, Path=TappedHandler, Mode=OneTime}"  -->


    <ContentView.Resources>
        <ResourceDictionary>

            <OnPlatform x:Key="NotifyTextMargin" x:TypeArguments="Thickness">
                <On Platform="iOS" Value="0,1,0,0" />
                <On Platform="Android" Value="0" />
            </OnPlatform>

        </ResourceDictionary>
    </ContentView.Resources>

    <ContentView.Content>

        <draw:Canvas
            x:Name="Wrapper"
            Padding="0,0,0,4"
            BackgroundColor="Transparent"
            Gestures="Enabled"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand">

            <draw:SkiaLayout
                x:Name="BtnBackground"
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <!--  ICON  -->
                <xam:FontIconLabelDrawn
                    x:Name="TabIcon"
                    Margin="0,0,0,0"
                    AddTranslationY="{Binding Source={x:Reference ThisBottomTab}, Path=AdjustIconPosition}"
                    FontSize="{Binding Source={x:Reference ThisBottomTab}, Path=AdjustedSize}"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Tag="TabIcon"
                    Text="{Binding Source={x:Reference ThisBottomTab}, Path=SvgImage}"
                    TextColor="{Binding Source={x:Reference ThisBottomTab}, Path=IconColor}"
                    TranslationX="{Binding Source={x:Reference ThisBottomTab}, Path=IconOffsetX}"
                    TranslationY="{Binding Source={x:Reference ThisBottomTab}, Path=IconOffsetY}"
                    UseCache="Operations"
                    VerticalOptions="Center" />

                <!--  TEXT  -->
                <draw:SkiaMarkdownLabel
                    d:Text="XoXo"
                    AddTranslationY="2"
                    FontFamily="FontTextTitle"
                    FontSize="{Binding Source={x:Reference ThisBottomTab}, Path=TextSize}"
                    HorizontalOptions="Center"
                    IsVisible="{Binding Source={x:Reference ThisBottomTab}, Path=ShowText}"
                    LineBreakMode="TailTruncation"
                    MaxLines="1"
                    Text="{Binding Source={x:Reference ThisBottomTab}, Path=Text}"
                    TextColor="{Binding Source={x:Reference ThisBottomTab}, Path=TextColor}"
                    UseCache="Operations"
                    VerticalOptions="End">
                    <!--<Label.Triggers>
                    <DataTrigger
                        Binding="{Binding Source={x:Reference ThisBottomTab}, Path=IsSelected}"
                        TargetType="Label"
                        Value="false">
                        <Setter Property="TextColor" Value="{Binding Source={x:Reference ThisBottomTab}, Path=UnselectedTextColor}" />
                    </DataTrigger>
                    <DataTrigger
                        Binding="{Binding Source={x:Reference ThisBottomTab}, Path=IsSelected}"
                        TargetType="Label"
                        Value="true">
                        <Setter Property="TextColor" Value="{Binding Source={x:Reference ThisBottomTab}, Path=SelectedTextColor}" />
                    </DataTrigger>
                </Label.Triggers>-->
                </draw:SkiaMarkdownLabel>

                <!--  NOTIFICATIONS  -->
                <!--<draw:SkiaShape
                Padding="0"
                BackgroundColor="{StaticResource Accent}"
                StrokeColor="{StaticResource Black}"
                CornerRadius="8.5"
                HeightRequest="17"
                HorizontalOptions="Center"
                IsClippedToBounds="True"
                IsVisible="{Binding Source={x:Reference ThisBottomTab}, Path=ShowThisNotifications}"
                TranslationX="8"
                TranslationY="0"
                VerticalOptions="Center"
                WidthRequest="17">

                <Label
                    Margin="{StaticResource NotifyTextMargin}"
                    d:Text="29"
                    FontFamily="FontTextBold"
                    FontSize="11.5"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    LineHeight="1"
                    Text="{Binding Source={x:Reference ThisBottomTab}, Path=NotificationsCount}"
                    TextColor="White"
                    VerticalOptions="Fill"
                    VerticalTextAlignment="Center" />

            </draw:SkiaShape>-->

                <draw:SkiaShape
                    x:Name="Cropper"
                    Margin="4,0"
                    CornerRadius="8"
                    HorizontalOptions="Fill"
                    IsClippedToBounds="True"
                    IsGhost="True"
                    Type="Rectangle"
                    VerticalOptions="Fill"
                    ZIndex="1" />

                <!--  OVERLAY EFFECT  TransformView="{x:Reference Cropper}"  -->
                <!--<partials:OneTimeEffect
                    BindableTrigger="{Binding Source={x:Reference TapHotspot}, Path=TotalDown}"
                    BackgroundColor="Transparent"
                    File="Lottie/tapsparks.json"
                    HorizontalOptions="Fill"
                    LockRatio="-1"
                    Repeat="0"
                    Tag="TabEffect"
                    AutoPlay="False"
                    TranslationY="-2"
                    ScaleX="1.3"
                    ScaleY="1.3"
                    VerticalOptions="Fill"
                    ZIndex="2" />

                <draw:SkiaHotspot
                    x:Name="TapHotspot"
                    HorizontalOptions="Fill"
                    Down="OnDown"
                    Tapped="OnTapped_Button"
                    AnimationTapped="None"
                    VerticalOptions="Fill"
                    ZIndex="3" />-->


                <draw:SkiaHotspot
                    x:Name="TapHotspot"
                    d:BackgroundColor="#33FF0000"
                    AnimationTapped="Ripple"
                    HorizontalOptions="Fill"
                    Tapped="OnTapped_Button"
                    TransformView="{x:Reference Cropper}"
                    VerticalOptions="Fill"
                    ZIndex="3" />


            </draw:SkiaLayout>

        </draw:Canvas>


    </ContentView.Content>







</tabs1:IconedTabItem>