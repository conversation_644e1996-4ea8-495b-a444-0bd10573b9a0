﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage
    x:Class="AppoMobi.UI.Dev.PageDev"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:viewModels="clr-namespace:AppoMobi.ViewModels"
    xmlns:views="clr-namespace:AppoMobi.Mobile.Views"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:DataType="viewModels:DevPageViewModel">

    <ContentPage.Content>
        <Grid RowDefinitions="*, Auto" VerticalOptions="Fill">


            <draw:Canvas
                Margin="0"
                BackgroundColor="Transparent"
                Gestures="Enabled"
                HorizontalOptions="Fill"
                VerticalOptions="Start">

                <!--<draw:SkiaShape
                        BackgroundColor="Green"
                        CornerRadius="25"
                        HorizontalOptions="Center"
                        LockRatio="1"
                        StrokeColor="Yellow"
                        StrokeWidth="1"
                        Type="Rectangle"
                        VerticalOptions="Center"
                        WidthRequest="50">

                        <draw:SkiaLayout
                            BackgroundColor="Red"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill"
                            ZIndex="-1" />

                    </draw:SkiaShape>-->

                <draw:SkiaShape
                    Margin="5"
                    Padding="0"
                    draw:AddGestures.CommandTapped="{Binding}"
                    CornerRadius="12"
                    HorizontalOptions="Fill"
                    StrokeColor="{x:Static xam:TextColors.EntryDesc}"
                    StrokeWidth="1"
                    Tag="Stroked">

                    <views:Area Tag="WrapperInsideShape">

                        <draw:SkiaLayout
                            BackgroundColor="Red"
                            HorizontalOptions="Fill"
                            IsVisible="True"
                            Tag="Gradient"
                            VerticalOptions="Fill"
                            ZIndex="-1" />

                        <draw:SkiaLabel
                            Margin="8"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="SelectedFilter"
                            TextColor="{x:Static xam:TextColors.PlaceholderActive}"
                            VerticalOptions="Center" />

                    </views:Area>


                </draw:SkiaShape>

            </draw:Canvas>


            <Button
                Grid.Row="1"
                Clicked="TappedButton"
                HeightRequest="40"
                Text="Debug"
                WidthRequest="150" />


        </Grid>
    </ContentPage.Content>

</ContentPage>