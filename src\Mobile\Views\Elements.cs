﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AppoMobi.Xam;

namespace AppoMobi.Mobile.Views
{

    public class Area : SkiaLayout
    {
        public Area()
        {
            HorizontalOptions = LayoutOptions.Fill;
        }
    }

    public class VStack : SkiaLayout
    {
        public VStack()
        {
            HorizontalOptions = LayoutOptions.Fill;
            Type = LayoutType.Column;
        }
    }

    public class HStack : SkiaLayout
    {
        public HStack()
        {
            Type = LayoutType.Row;
        }
    }


    public static class Elements
    {
        public static SkiaGradient ControlGradient
        {
            get
            {
                return new SkiaGradient()
                {
                    Type = GradientType.Linear,
                    Colors =new List<Color>()
                    {
                        BackColors.GradientStartNav,
                        BackColors.GradientEndNav
                    },
                };

            }
        }
    }

    public class DrawnFontIcon : SkiaLabel
    {
        public DrawnFontIcon()
        {
            UseCache = SkiaCacheType.Operations;
            FontFamily = "FaSolid";
        }

        public void UpdateSkin()
        {
            Invalidate();
        }


        private const string nameIconName = "IconName";
        public static readonly BindableProperty IconNameProperty = BindableProperty.Create(nameIconName, typeof(string), typeof(DrawnFontIcon), string.Empty); //, BindingMode.TwoWay
        public string IconName
        {
            get { return (string)GetValue(IconNameProperty); }
            set { SetValue(IconNameProperty, value); }
        }

        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)

        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {

                case nameIconName:
                    var maybePreset = FontIcons.GetPresetByName(IconName);
                    Preset = maybePreset;
                    UpdateSkin();
                    break;

                //property changed
                case namePreset:
                    if (Preset != null)
                    {
                        SetValue(IconNameProperty, Preset.GetName());
                        SetIcon(Preset);
                        //if (DeviceInfo.Current.Platform == DevicePlatform.Android)
                        //    TranslationY = Preset.TranslationY;
                        //else
                        //    TranslationY = -Preset.TranslationY;
                        //Scale = Preset.scale;
                        //Text = Preset.icon;
                        //DefaultFontOverride = Preset.FontOverride;
                    }
                    else
                    {
                        IconName = "";
                        //    if (DeviceInfo.Current.Platform == DevicePlatform.Android)
                        TranslationY = 0.0;
                        UpdateSkin();
                    }
                    break;


            }
        }


        public void SetIcon(FontIconsPreset value, double scale = -1, string fontOverride = null)

        {
            //   label.Preset = value;
            if (value == null)
            {
                Text = null;
            }
            else
            {
                if (DeviceInfo.Current.Platform == DevicePlatform.Android)
                    TranslationY = value.TranslationY;
                else
                    TranslationY = -value.TranslationY;

                if (scale < 0)
                    Scale = value.scale;
                else
                    Scale = scale;

                Text = value.icon;

                if (!string.IsNullOrEmpty(fontOverride))
                    FontFamily = value.FontOverride;
            }

            UpdateSkin();
            //label.TranslationY = value.TranslationY;
            //label.VerticalOptions = LayoutOptions.Center;
        }


        // Preset

        private const string namePreset = "Preset";
        public static readonly BindableProperty PresetProperty = BindableProperty.Create(namePreset, typeof(FontIconsPreset), typeof(DrawnFontIcon), null); //, BindingMode.TwoWay
        public FontIconsPreset Preset
        {
            get { return (FontIconsPreset)GetValue(PresetProperty); }
            set { SetValue(PresetProperty, value); }
        }

    }
}
