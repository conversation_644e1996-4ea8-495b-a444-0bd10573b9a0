﻿using AppoMobi.Forms.Common.ResX;
using AppoMobi.Forms.Controls;
using AppoMobi.Services;
using AppoMobi.UI;
using AppoMobi.ViewModels;
using AppoMobi.Xam;
using AppoMobi.Xam.Extensions;
using System;
using System.Collections.Generic;

namespace AppoMobi.Pages
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class ContentReciprocity
    {
        private ViewModelReciprocity viewModel;
        public override void OnDisappearing()
        {
            base.OnDisappearing();

            viewModel?.SaveValuesToSettings();
        }

        public ContentReciprocity(IPageEnhancedNav daddy)

        {
            InitializeComponent();
            Init(daddy);


            viewModel = new ViewModelReciprocity();
            BindingContext = viewModel;

            Daddy.RightIcon1Symbol.SetIcon(FontIcons.fa_info_circle);
            Daddy.ToggleButtonVisibility(ButtonType.Right1, true);

            SetupControls();
        }

        public override void OnUpdatedControls()
        {
            base.OnUpdatedControls();

            //bottomPadding.HeightRequest = Super.Screen.BottomInset;
        }

        private bool lock_down { get; set; }

        public void AnimateIcon(View view)

        {
            if (lock_down) return;
            lock_down = true;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Update the UI
                try
                {
                    // imgFx.FadeTo(0.75, 75);
                    //await view.ScaleTo(1.15, 75);
                    //await view.ScaleTo(1.0, 75);
                    await view.ScaleTo(1.25, 75);
                    await view.ScaleTo(1.0, 75);
                    lock_down = false;
                }
                catch (Exception e)
                {
                }
            });

        }

        public override void OnTabActivated()
        {
            base.OnTabActivated();

            var show = !Settings.Current.GetValueOrDefault("ReciprocityHintShown", false);

            if (show)
            {

                Core.ShowHelp(ResStrings.X_ReciprocityHelp);

                Settings.Current.AddOrUpdateValue("ReciprocityHintShown", true);
            }

        }





        public override void OnRightIcon1Clicked()

        {
            Core.ShowHelp(ResStrings.X_ReciprocityHelp);

            base.OnRightIcon1Clicked();
        }


        public int Mode { get; set; }


        public void SetupControls()
        {
            Recalculate();
        }
 
        private bool lockUpdate = false;

        private void OnChangedValued(object sender, EventArgs eventArgs)
        {
            if (!lockUpdate)
                Recalculate();
        }

        protected void SaveFields()
        {
            //if (Mode == 0)
            //{
            //    Settings.Current.AddOrUpdateValue("EntrySolutionA0", EntrySolutionA.Text);
            //    Settings.Current.AddOrUpdateValue("EntrySolutionB0", EntrySolutionB.Text);
            //    Settings.Current.AddOrUpdateValue("EntryWaterCalc", EntryWater.Text);
            //    Settings.Current.AddOrUpdateValue("EntryDeveloper0", EntryDeveloper.Text);

            //    var check = Settings.Current.GetValueOrDefault("EntrySolutionA0", "0");
            //    check = Settings.Current.GetValueOrDefault("EntrySolutionB0", "0");
            //    check = Settings.Current.GetValueOrDefault("EntryWaterCalc", "0");
            //    check = Settings.Current.GetValueOrDefault("EntryDeveloper0", "0");

            //}
            //else
            //{
            //    Settings.Current.AddOrUpdateValue("EntrySolutionA1", EntrySolutionA.Text);
            //    Settings.Current.AddOrUpdateValue("EntrySolutionB1", EntrySolutionB.Text);
            //    Settings.Current.AddOrUpdateValue("EntryWaterFit", EntryWater.Text);
            //    Settings.Current.AddOrUpdateValue("EntryDeveloper1", EntryDeveloper.Text);

            //    var check =  Settings.Current.GetValueOrDefault("EntryDeveloper1", "666");

            //}
        }


        protected void Recalculate()

        {
            lockUpdate = true;



            try
            {

                // //B
                // int inputSolutionB = EntrySolutionB.Text.ToInteger(); //parts
                // if (inputSolutionB < 1) throw new Exception();

                // //A
                // int inputSolutionA = EntrySolutionA.Text.ToInteger(); //parts
                // int inputWater = EntryWater.Text.ToInteger(); //mm
                // int inputDeveloper = EntryDeveloper.Text.ToInteger(); //mm 

                // int K1 = inputDeveloper / inputWater;
                // var SolutionA = K1 * inputSolutionA;
                // var SolutionB = K1 * inputSolutionB;
                // var Water = K1 * inputWater;
                // var Developer = SolutionA + SolutionB + Water;


                // if (Developer == 0)
                //     throw new Exception();

                // while (Developer < inputDeveloper)
                // {
                //     K1++;
                //     SolutionA = K1 * inputSolutionA;
                //     SolutionB = K1 * inputSolutionB;
                //     Water = K1 * inputWater;
                //     Developer = SolutionA + SolutionB + Water;
                // }

                // while (Developer > inputDeveloper)
                // {
                //     K1--;
                //     SolutionA = K1 * inputSolutionA;
                //     SolutionB = K1 * inputSolutionB;
                //     Water = K1 * inputWater;
                //     Developer = SolutionA + SolutionB + Water;
                // }

                // cSolutionAOut.Text = $"{K1 * inputSolutionA}";
                // cSolutionBOut.Text = $"{K1 * inputSolutionB}";
                // cWaterOut.Text = $"{K1 * inputWater}";
                // cDeveloperOut.Text = $"{K1 * inputSolutionA + K1 * inputSolutionB + K1 * inputWater}";


                //SaveFields();

                // var tt = cIcons.Width;
            }
            catch (Exception exception)
            {
                //cSolutionAOut.Text = "?";
                //cSolutionBOut.Text = "?";
                //cWaterOut.Text = "?";
                //cDeveloperOut.Text = "?";

            }


            lockUpdate = false;
        }

        #region Picker FILM





        public static List<OptionsListItem> OptionsFilms = new List<OptionsListItem>();

        private void TappedSelectorFilm(object? sender, SkiaControl.ControlTappedEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                if (OptionsFilms.Count == 0)
                {
                    foreach (var line in viewModel.FilmsListOrdered)
                    {
                        OptionsFilms.Add(new OptionsListItem
                        {
                            ActionDesc = line,
                            SwitchValue = viewModel.SelectedFilm == line,
                            Switched = (e) =>
                            {
                                viewModel.SelectedFilm = line;
                            }
                        });
                    }
                }

                var show = new OptionsPresenter(OptionsFilms);
                var selected = await show.ShowGetOne(ResStrings.Films);
                selected?.Switched?.Invoke(true);
            });

    
        }

        #endregion



        #region Picker FILTER

        public static List<OptionsListItem> OptionsFilters = new List<OptionsListItem>();

        private void TappedSelectorFIlter(object? sender, SkiaControl.ControlTappedEventArgs e)
        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                if (OptionsFilters.Count == 0)
                {
                    foreach (var line in viewModel.FiltersListOrdered)
                    {
                        OptionsFilters.Add(new OptionsListItem
                        {
                            ActionDesc = line,
                            SwitchValue = viewModel.SelectedFilter == line,
                            Switched = (e) =>
                            {
                                viewModel.SelectedFilter = line;
                            }
                        });
                    }
                }

                var show = new OptionsPresenter(OptionsFilters);
                var selected = await show.ShowGetOne(ResStrings.Filters);
                selected?.Switched?.Invoke(true);
            });

        }

        //private async void OnTapped_PickerFilter(object sender, XamTapEventArgs xamTapEventArgs)
        //{
        //    ControlPickerFilter.DisableSpamClicks();

        //    if (OptionsFilters.Count == 0)
        //    {
        //        foreach (var line in viewModel.FiltersListOrdered)
        //        {
        //            OptionsFilters.Add(new OptionsListItem
        //            {
        //                ActionDesc = line,
        //                SwitchValue = viewModel.SelectedFilter == line,
        //                Switched = (e) =>
        //                {
        //                    viewModel.SelectedFilter = line;
        //                }
        //            });
        //        }
        //    }

        //    var show = new OptionsPresenter(OptionsFilters);
        //    var selected = await show.ShowGetOne(ResStrings.Filters);
        //    selected?.Switched?.Invoke(true);
        //}

        #endregion




        //private void PickerFilter_OnUnfocused(object sender, FocusEventArgs e)
        //{
        //    DropdownFilterVisible = false;
        //}
        //private void PickerFillm_OnUnfocused(object sender, FocusEventArgs e)
        //{
        //    DropdownFilmVisible = false;
        //}

        private void DrawnView_OnWillFirstTimeDraw(object? sender, SkiaDrawingContext? e)
        {
            if (BindingContext is ViewModelReciprocity vm)
            {
                //Picker.DataSource = vm.ExposureMinutesSource;
            }
        }

        //private async void OnBtnTimer_Clicked(object sender, EventArgs e)
        //{
        //    BtnTimer.DisableSpamClicks();

        //    await viewModel.StartTimer();
        //}

        private async void TappedTimer(object? sender, SkiaControl.ControlTappedEventArgs e)
        {
            await viewModel.StartTimer();
        }
    }

}
