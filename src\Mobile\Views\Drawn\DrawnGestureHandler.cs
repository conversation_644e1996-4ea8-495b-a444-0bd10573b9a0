﻿using AppoMobi.Touch;
using DrawnUi.Draw;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AppoMobi.Mobile.Import.Controls.Drawn
{


    public class DrawnGestureHandler : GestureHandler
    {
        public DrawnGestureHandler(IWithTouch element) : base(element)
        {

        }

        public override bool OnDown(DownUpEventArgs args)
        {
            if (this.HandlesDown)
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    base.OnDown(args);
                });
                return true;
            }
            return false;
        }

        public override bool OnUp(DownUpEventArgs args)
        {
            if (this.HandlesUp)
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    base.OnUp(args);
                });
                return true;
            }
            return false;

        }

        public override bool OnTapped(TapEventArgs args)
        {
            if (this.HandlesTapped)
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    base.OnTapped(args);
                });
                return true;
            }
            return false;
        }
    }
}
