﻿<?xml version="1.0" encoding="utf-8" ?>

<pages:IncludedContent
    x:Class="AppoMobi.Main.ContentDevelop"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:views="using:AppoMobi.Mobile.Views"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="MainScroll"
    Padding="20,0,20,0"
    HorizontalOptions="Fill">


    <VerticalStackLayout
        HorizontalOptions="FillAndExpand"
        Spacing="16"
        VerticalOptions="Start">

        <!--  Focal Length фокусное расстояние объектива  -->
        <!--  переключатель между дюймами и мм  -->


        <!--  Bellows Extension длина меха в мм  -->
        <!--  переключатель между дюймами и мм  -->

        <!--  entries  -->
        <StackLayout
            x:Name="cRegister"
            HorizontalOptions="Fill"
            Spacing="8"
            VerticalOptions="Start">

            <!--  SEGMENTED SELECTOR  -->
            <draw:Canvas
                Margin="4,25,4,5"
                BackgroundColor="Transparent"
                Gestures="Enabled"
                HorizontalOptions="Fill">

                <draw:SkiaShape
                    Margin="1,0,1,0"
                    CornerRadius="7"
                    HorizontalOptions="Fill"
                    StrokeColor="#DD999999"
                    StrokeWidth="1"
                    Tag="Stroked"
                    UseCache="Image">

                    <draw:SkiaLayout
                        ColumnDefinitions="50*,1,50*"
                        ColumnSpacing="0"
                        DefaultRowDefinition="*"
                        HeightRequest="34"
                        HorizontalOptions="Fill"
                        Type="Grid"
                        UseCache="Operations">

                        <!--  0  -->
                        <draw:SkiaLayout
                            x:Name="cHilight0"
                            BackgroundColor="Red"
                            FillGradient="{x:Static views:Elements.ControlGradient}"
                            HorizontalOptions="Fill"
                            IsVisible="True"
                            Opacity="0.25"
                            VerticalOptions="Fill"
                            ZIndex="-1" />

                        <draw:SkiaLabel
                            x:Name="cSegmentLabel0"
                            Margin="4"
                            AutoSize="FitHorizontal"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="{x:Static resX:ResStrings.X_FromGiven}"
                            TextColor="{x:Static xam:TextColors.PlaceholderActive}"
                            VerticalOptions="Center" />

                        <draw:SkiaHotspot
                            Grid.Column="0"
                            AnimationTapped="Ripple"
                            HorizontalOptions="Fill"
                            Tapped="OnTapped_Tab1"
                            TransformView="{x:Reference cHilight0}"
                            VerticalOptions="Fill" />


                        <!--  line  -->
                        <draw:SkiaControl
                            Grid.Column="1"
                            BackgroundColor="#DD999999"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill" />

                        <!--  1  -->
                        <draw:SkiaLayout
                            x:Name="cHilight1"
                            Grid.Column="2"
                            BackgroundColor="Red"
                            FillGradient="{x:Static views:Elements.ControlGradient}"
                            HorizontalOptions="Fill"
                            IsVisible="True"
                            Opacity="0.25"
                            VerticalOptions="Fill"
                            ZIndex="-1" />

                        <draw:SkiaLabel
                            x:Name="cSegmentLabel1"
                            Grid.Column="2"
                            Margin="4"
                            AutoSize="FitHorizontal"
                            FontSize="14"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            LineBreakMode="TailTruncation"
                            Text="{x:Static resX:ResStrings.X_WithinVolume}"
                            TextColor="{x:Static xam:TextColors.PlaceholderActive}"
                            VerticalOptions="Center" />

                        <draw:SkiaHotspot
                            Grid.Column="2"
                            AnimationTapped="Ripple"
                            HorizontalOptions="Fill"
                            Tapped="OnTapped_Tab2"
                            TransformView="{x:Reference cHilight1}"
                            VerticalOptions="Fill" />


                    </draw:SkiaLayout>


                </draw:SkiaShape>

            </draw:Canvas>


            <!--  HEADER  -->
            <Grid
                ColumnSpacing="6"
                HorizontalOptions="FillAndExpand"
                RowSpacing="0"
                VerticalOptions="Start">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="100" />
                </Grid.ColumnDefinitions>

                <!--<Label  Margin="0,2,0,0" Grid.ColumnSpan="2"
                    TextColor="{x:Static xam:TextColors.Placeholder}"
                    FontSize ="12" HorizontalOptions="Center"
                    Text="Параметры"/>-->


                <Label
                    Grid.Column="2"
                    Margin="0,2,0,0"
                    FontSize="12"
                    HorizontalOptions="Center"
                    Text="{x:Static resX:ResStrings.X_ResultMl}"
                    TextColor="{x:Static xam:TextColors.EntryDesc}" />

            </Grid>

            <!--  ENTRY LINES  -->
            <Grid
                ColumnSpacing="6"
                HorizontalOptions="FillAndExpand"
                RowSpacing="0"
                VerticalOptions="Start">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="75" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="104" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="80" />
                    <RowDefinition Height="80" />
                    <RowDefinition Height="80" />
                    <RowDefinition Height="80" />
                </Grid.RowDefinitions>

                <!--<Grid.RowDefinitions>
                <RowDefinition Height="100"/>

            </Grid.RowDefinitions>-->

                <!--  ROW 0  -->
                <Grid VerticalOptions="Start">
                    <xam:FontIconLabel
                        x:Name="cIcon1"
                        Margin="10"
                        FontSize="35"
                        HorizontalOptions="Center"
                        IconName="fa_fill_drip"
                        TextColor="{x:Static xam:TextColors.EntryDesc}" />
                    <Label
                        Margin="10,50,10,10"
                        FontSize="12"
                        HorizontalOptions="Center"
                        Text="{x:Static resX:ResStrings.X_SolutionA}"
                        TextColor="{x:Static xam:TextColors.EntryDesc}"
                        VerticalOptions="Start" />
                </Grid>

                <Grid
                    Grid.Column="1"
                    Margin="0,8,0,0"
                    VerticalOptions="Start">

                    <xam:UnderlinedEntry
                        x:Name="EntrySolutionA"
                        FadeUnfocused="True"
                        HorizontalOptions="Fill"
                        ReadOnly="True"
                        VerticalOptions="Start" />

                    <!--  hotspot  -->
                    <gestures:LegacyGesturesBoxView
                        Margin="5"
                        Down="OnTapped_EntrySolutionA"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill" />

                </Grid>

                <Grid Grid.Column="2" VerticalOptions="Fill">
                    <xam:FontIconLabel
                        x:Name="cArrow1"
                        Margin="0,10,0,10"
                        FontSize="14"
                        HorizontalOptions="Start"
                        IconName="fa_arrow_alt_right"
                        Opacity="0.5"
                        TextColor="{x:Static xam:TextColors.EntryDesc}"
                        VerticalOptions="Center" />

                    <Label
                        x:Name="cSolutionAOut"
                        Margin="19,0,10,0"
                        FontSize="30"
                        HorizontalOptions="Start"
                        Text="42"
                        TextColor="{x:Static xam:TextColors.Result}"
                        VerticalOptions="Center" />

                </Grid>

                <!--  ROW 1  -->
                <Grid Grid.Row="1" VerticalOptions="Start">
                    <xam:FontIconLabel
                        x:Name="cIcon1b"
                        Margin="10"
                        FontSize="35"
                        HorizontalOptions="Center"
                        IconName="fa_fill_drip"
                        TextColor="{x:Static xam:TextColors.EntryDesc}" />
                    <Label
                        Margin="10,50,10,10"
                        FontSize="12"
                        HorizontalOptions="Center"
                        Text="{x:Static resX:ResStrings.X_SolutionB}"
                        TextColor="{x:Static xam:TextColors.EntryDesc}"
                        VerticalOptions="Start" />
                </Grid>
                <Grid
                    Grid.Row="1"
                    Grid.Column="1"
                    Margin="0,8,0,0"
                    VerticalOptions="Start">
                    <xam:UnderlinedEntry
                        x:Name="EntrySolutionB"
                        FadeUnfocused="True"
                        HorizontalOptions="Fill"
                        ReadOnly="True"
                        Text="1"
                        VerticalOptions="Start" />

                </Grid>
                <Grid
                    Grid.Row="1"
                    Grid.Column="2"
                    VerticalOptions="Fill">
                    <xam:FontIconLabel
                        x:Name="cArrow4"
                        Margin="0,10,0,10"
                        FontSize="14"
                        HorizontalOptions="Start"
                        IconName="fa_arrow_alt_right"
                        Opacity="0.5"
                        TextColor="{x:Static xam:TextColors.EntryDesc}"
                        VerticalOptions="Center" />
                    <Label
                        x:Name="cSolutionBOut"
                        Margin="19,0,10,0"
                        FontSize="30"
                        HorizontalOptions="Start"
                        Text="21"
                        TextColor="{x:Static xam:TextColors.Result}"
                        VerticalOptions="Center" />
                </Grid>

                <!--  ROW 2  -->
                <Grid
                    x:Name="cTest"
                    Grid.Row="2"
                    VerticalOptions="Start">
                    <xam:FontIconLabel
                        x:Name="cIcon2"
                        Margin="10"
                        FontSize="35"
                        HorizontalOptions="Center"
                        IconName="fa_fill_drip"
                        TextColor="{x:Static xam:TextColors.EntryDesc}" />
                    <Label
                        Margin="10,50,10,10"
                        FontSize="12"
                        HorizontalOptions="Center"
                        Text="{x:Static resX:ResStrings.X_Water}"
                        TextColor="{x:Static xam:TextColors.EntryDesc}" />
                </Grid>
                <Grid
                    Grid.Row="2"
                    Grid.Column="1"
                    Margin="0,8,0,0"
                    VerticalOptions="Start">
                    <xam:UnderlinedEntry
                        x:Name="EntryWater"
                        FadeUnfocused="True"
                        HorizontalOptions="Fill"
                        VerticalOptions="Start" />

                    <!--<xam:FontIconLabel x:Name="cIcon1" HorizontalOptions="Start" VerticalOptions="Center"  Margin="0,0,0,14"/>-->

                    <!--<Label x:Name="cDesc1"
                       HorizontalOptions="End"
                       FontSize = "24"
                       Text="частей" VerticalOptions="Start" Margin="0,20,0,0"/>-->

                </Grid>
                <Grid
                    Grid.Row="2"
                    Grid.Column="2"
                    VerticalOptions="Fill">
                    <xam:FontIconLabel
                        x:Name="cArrow2"
                        Margin="0,10,0,10"
                        FontSize="14"
                        HorizontalOptions="Start"
                        IconName="fa_arrow_alt_right"
                        Opacity="0.5"
                        TextColor="{x:Static xam:TextColors.EntryDesc}"
                        VerticalOptions="Center" />
                    <Label
                        x:Name="cWaterOut"
                        Margin="19,0,10,0"
                        FontSize="30"
                        HorizontalOptions="Start"
                        LineBreakMode="NoWrap"
                        MaxLines="1"
                        Text="1260"
                        TextColor="{x:Static xam:TextColors.Result}"
                        VerticalOptions="Center" />

                </Grid>

                <!--  ROW  -->
                <Grid Grid.Row="3" VerticalOptions="StartAndExpand">
                    <xam:FontIconLabel
                        x:Name="cIcon3"
                        Margin="10"
                        FontSize="35"
                        HorizontalOptions="Center"
                        IconName="fa_fill_drip"
                        TextColor="{x:Static xam:TextColors.EntryDesc}" />
                    <Label
                        x:Name="cIcons"
                        Margin="10,50,10,0"
                        FontSize="12"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{x:Static resX:ResStrings.X_SolutionResult}"
                        TextColor="{x:Static xam:TextColors.EntryDesc}" />
                </Grid>
                <Grid
                    Grid.Row="3"
                    Grid.Column="1"
                    Margin="0,8,0,0"
                    VerticalOptions="Start">


                    <xam:UnderlinedEntry
                        x:Name="EntryDeveloper"
                        FadeUnfocused="True"
                        HorizontalOptions="Fill"
                        VerticalOptions="Start" />

                    <!--<xam:FontIconLabel x:Name="cIcon1" HorizontalOptions="Start" VerticalOptions="Center"  Margin="0,0,0,14"/>-->

                    <!--<Label x:Name="cDesc1"
                       HorizontalOptions="End"
                       FontSize = "24"
                       Text="частей" VerticalOptions="Start" Margin="0,20,0,0"/>-->

                </Grid>
                <Grid
                    Grid.Row="3"
                    Grid.Column="2"
                    VerticalOptions="Fill">
                    <xam:FontIconLabel
                        x:Name="cArrow3"
                        Margin="0,10,0,10"
                        FontSize="14"
                        HorizontalOptions="Start"
                        IconName="fa_arrow_alt_right"
                        Opacity="0.5"
                        TextColor="{x:Static xam:TextColors.EntryDesc}"
                        VerticalOptions="Center" />
                    <Label
                        x:Name="cDeveloperOut"
                        Margin="19,0,10,0"
                        FontSize="30"
                        HorizontalOptions="Start"
                        Text="1323"
                        TextColor="{x:Static xam:TextColors.Result}"
                        VerticalOptions="Center" />

                </Grid>

            </Grid>


        </StackLayout>

        <Label
            x:Name="cHelp"
            Margin="0,10,0,0"
            FontSize="12"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center"
            LineBreakMode="WordWrap"
            Text="This is a help text."
            TextColor="{x:Static xam:TextColors.EntryDesc}"
            WidthRequest="200" />


    </VerticalStackLayout>


</pages:IncludedContent>