﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:IncludedContent
    x:Class="AppoMobi.Main.Content35mm"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:main="clr-namespace:AppoMobi.Main"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="MainScroll"
    Padding="20,0,20,0">

    <VerticalStackLayout Spacing="16" VerticalOptions="Start">

        <!--  Focal Length фокусное расстояние объектива  -->
        <!--  переключатель между дюймами и мм  -->




        <!--  Bellows Extension длина меха в мм  -->
        <!--  переключатель между дюймами и мм  -->

        <!--  entries  -->
        <StackLayout
            HorizontalOptions="Fill"
            Spacing="10"
            VerticalOptions="Start">

            <StackLayout
                x:Name="cFstopStack"
                Margin="0,20,0,10"
                HorizontalOptions="Fill"
                Spacing="4">

                <Label
                    FontSize="24"
                    HorizontalOptions="Center"
                    Text="{x:Static resX:ResStrings.X_35mmResult}"
                    TextColor="{x:Static xam:TextColors.EntryDesc}" />

                <Label
                    x:Name="cOutput"
                    FontSize="55"
                    HorizontalOptions="Center"
                    TextColor="{x:Static xam:TextColors.Result}" />

                <!--  select units  -->
                <Grid
                    x:Name="ContainerUnitsResult"
                    Margin="0,-8,0,0"
                    HorizontalOptions="Fill">
                    <Grid HorizontalOptions="Center">

                        <Label
                            x:Name="cUnitsResult"
                            Margin="8,0,8,0"
                            LineBreakMode="NoWrap"
                            Text="mm" />

                        <!--  hotspot  -->
                        <gestures:LegacyGesturesBoxView
                            Margin="0,0,0,0"
                            Down="UnitsFocalResult_OnTapped"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill" />
                    </Grid>

                </Grid>

            </StackLayout>


            <!--  entry  -->
            <Grid HorizontalOptions="Center" WidthRequest="250">

                <xam:UnderlinedEntry
                    x:Name="EntryFocal"
                    FadeUnfocused="True"
                    HorizontalOptions="Fill" />

                <!--<xam:FontIconLabel x:Name="cIcon1" HorizontalOptions="Start" VerticalOptions="Center"  Margin="0,0,0,14"/>-->


                <Label
                    x:Name="cDesc1"
                    Margin="0,10,0,0"
                    HorizontalOptions="End"
                    Text="''"
                    VerticalOptions="Center" />

                <!--  hotspot  -->
                <gestures:LegacyGesturesBoxView
                    Margin="0,0,0,4"
                    Down="UnitsFocal_OnTapped"
                    HeightRequest="40"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    WidthRequest="60" />

            </Grid>


            <Label
                Margin="2,16,2,0"
                FontSize="12"
                HorizontalOptions="Center"
                Text="{x:Static resX:ResStrings.X_FrameFormat}"
                TextColor="{x:Static xam:TextColors.EntryDesc}" />

            <!--  SUB CATEGORIES  -->
            <ContentView>
                <xam:NiftyDataGrid
                    x:Name="DataGrid"
                    Margin="1"
                    ColumnSpacing="8"
                    Columns="4"
                    HorizontalOptions="Center"
                    RowSpacing="8">
                    <xam:NiftyDataGrid.ItemTemplate>
                        <DataTemplate>

                            <main:Cell35mmDataGrid ItemTapped="OnTapped_Item" />

                        </DataTemplate>
                    </xam:NiftyDataGrid.ItemTemplate>

                </xam:NiftyDataGrid>
            </ContentView>




        </StackLayout>

        <Label
            x:Name="cHelp"
            Margin="0,16,0,0"
            FontSize="12"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center"
            LineBreakMode="WordWrap"
            Text="This is a help text."
            TextColor="{x:Static xam:TextColors.EntryDesc}"
            WidthRequest="200" />

    </VerticalStackLayout>
</pages:IncludedContent>