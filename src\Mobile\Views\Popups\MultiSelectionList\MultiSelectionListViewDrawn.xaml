﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage
    x:Class="AppoMobi.Xam.MultiSelectionListViewDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:AppoMobi.Mobile.Import.Controls.Drawn"
    xmlns:pages="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:DataType="xam:MultiSelectionListViewDrawn"
    CanBeDismissedByTappingOutsideOfPopup="True"
    HorizontalOptions="Fill"
    IgnoreSafeArea="True"
    VerticalOptions="Fill"
    Color="Transparent">

    <!--<pages:PopupPage.Animation>
        <animations:MoveAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"
            PositionIn="Right"
            PositionOut="Right" />
    </pages:PopupPage.Animation>-->

    <ContentView HorizontalOptions="Fill" VerticalOptions="Fill">

        <draw:Canvas
            x:Name="MainCanvas"
            BackgroundColor="Transparent"
            Gestures="Enabled"
            
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Tapped="SkiaControl_OnTapped"
                VerticalOptions="Fill">

                <draw:SkiaLayout
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    WidthRequest="250">

                    <!--  background hotspot  -->
                    <!--<drawn:TouchSkiaLayout
                Tapped="Grid_OnDown"
                HorizontalOptions="Fill"
                VerticalOptions="Fill" />-->

                    <draw:SkiaLayout
                        BackgroundColor="{x:Static xam:BackColors.OptionLine}"
                        HorizontalOptions="Fill"
                        Spacing="0"
                        Type="Column">

                        <!--  HEADER  -->
                        <draw:SkiaLayout
                            HorizontalOptions="Fill"
                            InputTransparent="True"
                            UseCache="Image">

                            <draw:SkiaLayout.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                    <GradientStop Offset="0.0" Color="{x:Static xam:BackColors.GradientStartNav}" />
                                    <GradientStop Offset="1.0" Color="{x:Static xam:BackColors.GradientEndNav}" />
                                </LinearGradientBrush>
                            </draw:SkiaLayout.Background>

                            <!--<draw:SkiaShape
                      HeightRequest="24"
                      HorizontalOptions="Fill"
                      MinimumHeightRequest="24"
                      VerticalOptions="Center" />-->

                            <draw:SkiaShape
                                BackgroundColor="#33000000"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill" />

                            <!--  header text  -->
                            <draw:SkiaMarkdownLabel
                                x:Name="cTitle"
                                Margin="20,8,8,8"
                                FontSize="15"
                                HorizontalOptions="Fill"
                                Text="{Binding Title}"
                                TextColor="WhiteSmoke"
                                VerticalOptions="Center" />

                        </draw:SkiaLayout>

                        <draw:SkiaScroll HorizontalOptions="Fill">

                            <draw:SkiaLayout
                                HorizontalOptions="Fill"
                                ItemsSource="{Binding MenuList}"
                                Spacing="0.75"
                                Tag="Popup"
                                Type="Column"
                                UseCache="Image"
                                VerticalOptions="Start">

                                <draw:SkiaLayout.ItemTemplate>
                                    <DataTemplate x:DataType="xam:SelectionListItem">

                                        <drawn:TouchSkiaLayout
                                            x:Name="ControlMenu"
                                            ColumnDefinitions="30,*"
                                            Down="OnDownCell"
                                            DownCommandParameter="{Binding .}"
                                            HorizontalOptions="Fill"
                                            Spacing="0"
                                            Tapped="OnTapped_Item"
                                            Up="OnUpCell"
                                            UseCache="None"
                                            VerticalOptions="Start">

                                            <draw:SkiaMarkdownLabel
                                                Margin="16,0,0,0"
                                                FontSize="17"
                                                IsVisible="{Binding Selected}"
                                                Text="&#x2713;"
                                                TextColor="{x:Static xam:TextColors.GreyDark}"
                                                VerticalOptions="Center" />

                                            <draw:SkiaMarkdownLabel
                                                Margin="38,15,4,15"
                                                FontSize="15"
                                                HorizontalOptions="Fill"
                                                Text="{Binding Title}"
                                                TextColor="{x:Static xam:TextColors.GreyDark}"
                                                VerticalOptions="Center" />

                                            <!--  divider  -->
                                            <draw:SkiaShape
                                                BackgroundColor="LightGray"
                                                HeightRequest="1"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="End" />

                                        </drawn:TouchSkiaLayout>

                                    </DataTemplate>
                                </draw:SkiaLayout.ItemTemplate>


                            </draw:SkiaLayout>
                        </draw:SkiaScroll>

                    </draw:SkiaLayout>

                </draw:SkiaLayout>

            </draw:SkiaLayout>
        </draw:Canvas>

    </ContentView>


</pages:PopupPage>