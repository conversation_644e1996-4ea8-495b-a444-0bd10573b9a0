﻿using AppoMobi.Mobile.Views.Popups;
using System;
using System.Threading.Tasks;


namespace AppoMobi.Auth
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class WaitingPopup : PopupPage
    {
        public string Message { get; set; }


        public WaitingPopup(string message)
        {
            InitializeComponent();
            Animation = null;
            Message = message;
            ControlMessage.Text = Message;

            if (DeviceInfo.Current.Platform != DevicePlatform.Android)
            {
                Spinner.Scale = 1.75;
                //ControlMessageContainer.BorderColor = AppColors.BwGreyLight;
            }
        }

        // Invoced when background is clicked
        protected override bool OnBackgroundClicked()
        {
            // Return default value - CloseWhenBackgroundIsClicked
            //   return base.OnBackgroundClicked();
            return false;
        }



    }
}