﻿<?xml version="1.0" encoding="utf-8" ?>

<pages:IncludedContent
    x:Class="AppoMobi.Forms.Content.Camera.ContentCameraZoom"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:camera="clr-namespace:AppoMobi.Forms.Content.Camera"
    xmlns:controls="clr-namespace:AppoMobi.Forms.Controls"
    xmlns:controls1="clr-namespace:AppoMobi.Forms.Content.Camera.Controls"
    xmlns:d="http://xamarin.com/schemas/2014/forms/design"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:main="clr-namespace:AppoMobi.Main"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="ThisPage"
    Margin="0"
    Padding="0"
    x:DataType="main:MainVModel"
    BackgroundColor="{x:Static appoMobi:AppColors.Primary}"
    mc:Ignorable="d">

    <Grid
        HeightRequest="{Binding Source={x:Reference ThisPage}, Path=Height}"
        IsClippedToBounds="True"
        RowDefinitions="*"
        VerticalOptions="Start">

        <!--  PRESET SELECTOR HIDDEN PICKER  -->
        <xam:SystemPicker
            x:Name="PickerPresets"
            HeightRequest="1"
            OnSelected="OnIndexChanged_PresetFilter"
            VerticalOptions="Start" />

        <!--  CAMERA VIEW  -->
        <controls1:CameraPreview
            x:Name="Camera"
            BackgroundColor="Gray"
            CustomAlbum="{Binding CustomAlbum}"
            DisplayMode="Fill"
            Filter="None"
            Geotag="{Binding Geotag}"
            HeightRequest="{Binding Source={x:Reference ThisPage}, Path=Height}"
            HorizontalOptions="Fill"
            IsEnabled="False"
            ManualZoom="True"
            NeedCalibration="True"
            Position="Default"
            StyleId="Viewfinder"
            Type="Max"
            VerticalOptions="Fill" />

        <!--  FRAMES OVERLAY  -->
        <controls1:ViewportFrames
            x:Name="ViewportFrames"
            AnnotationColor="#ffffff"
            EnableTouchEvents="False"
            FogColor="#ee666666"
            FogEnabled="{Binding Source={x:Reference ThisPage}, Path=FogEnabled}"
            FontFamily="{StaticResource FileFontDefault}"
            FramesColor="#bbffffff"
            HorizontalOptions="Fill"
            InputTransparent="True"
            IsVisible="True"
            ItemsSource="{Binding Source={x:Reference ThisPage}, Path=PresetFrames}"
            OnInitialized="ViewportFrames_OnOnInitialized"
            SensorRotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
            VerticalOptions="Fill"
            ViewportFocalLength="{Binding Source={x:Reference Camera}, Path=FocalLengthAdjusted}" />


        <!--  FPS  -->
        <Label
            Margin="0,80,0,0"
            d:IsVisible="{Binding Source={x:Reference ThisPage}, Path=IsDebug}"
            BackgroundColor="Black"
            FontAttributes="Bold"
            FontSize="12"
            HorizontalOptions="Center"
            IsVisible="{Binding Source={x:Reference ThisPage}, Path=IsDebug}"
            Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
            Text="{Binding Source={x:Reference Camera}, Path=FPS}"
            TextColor="GreenYellow"
            VerticalOptions="Start" />

        <!--<Label
            Margin="20"
            FontAttributes="Bold"
            FontSize="12"
            HorizontalOptions="Center"
            Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
            Text="{Binding Source={x:Reference ThisPage}, Path=ZoomDesc}"
            TextColor="Aqua" />-->

        <!--  PRESET TITLE  -->
        <!--<Label
            Margin="0,20,0,0"
            FontAttributes="Bold"
            FontSize="12"
            HorizontalOptions="Center"
            Text="{Binding Source={x:Reference ThisPage}, Path=CurrentFormat.Title, StringFormat='[{0}]'}"
            TextColor="GreenYellow" />-->


        <!--  CAMERA BTNS  -->
        <StackLayout
            x:Name="ButtonsStack"
            Margin="0,0,0,30"
            HorizontalOptions="Center"
            IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings, Converter={x:StaticResource NotConverter}}"
            Orientation="Horizontal"
            Spacing="{Binding Source={x:Reference ThisPage}, Path=ButtonsSpacing}"
            VerticalOptions="End">

            <!--<controls:TouchFrame Style="{StaticResource CameraButton}" Tapped="OnTapped_Switch">
                        <xam:FontIconLabel Style="{StaticResource CameraIcon}" Text="{x:Static xam:FaPro.CodeCommit}" />
                    </controls:TouchFrame>-->


            <!--<controls:TouchFrame Style="{StaticResource CameraButton}" Tapped="OnTapped_Pick">
                        <xam:FontIconLabel
                            Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={camera:OrientationToRotationConverter}}"
                            Style="{StaticResource CameraIcon}"
                            Text="{x:Static xam:FaPro.EyeDropper}" />
                    </controls:TouchFrame>-->


            <!--  BTN SETTINGS  -->
            <controls:TouchFrame
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_Lenses"
                TimeLockDownMs="1000">
                <xam:FontIconLabel
                    Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
                    Style="{StaticResource CameraIcon}"
                    Text="{x:Static xam:FaPro.Cog}" />

            </controls:TouchFrame>

            <controls:TouchFrame
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_Settings"
                TimeLockDownMs="1000">
                <xam:FontIconLabel
                    Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
                    Style="{StaticResource CameraIcon}"
                    Text="{x:Static xam:FaPro.Screwdriver}" />

            </controls:TouchFrame>


            <!--  BTN BW  -->
            <!--  SvgIcon  -->
            <draw:Canvas
                Gestures="Lock"
                HeightRequest="44"
                HorizontalOptions="Center"
                Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                VerticalOptions="Center"
                WidthRequest="44">

                <camera:TouchDrawnShape
                    BackgroundColor="#99FFFFFF"
                    HorizontalOptions="Fill"
                    StrokeColor="#33000000"
                    StrokeWidth="1"
                    Tapped="OnTapped_Filter"
                    Type="Circle"
                    UseCache="Image"
                    VerticalOptions="Fill">

                    <draw:SkiaSvg
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        Opacity="0.525"
                        SvgString="{Binding Source={x:Reference ThisPage}, Path=IconEffect}"
                        UseGradient="False"
                        VerticalOptions="Center"
                        WidthRequest="24" />

                </camera:TouchDrawnShape>

            </draw:Canvas>

            <!--<controls:TouchFrame
                x:Name="ControlPresetPickerPresets"
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_Filter"
                TimeLockDownMs="1000">

                <svg1:SvgIcon
                    HeightRequest="24"
                    HorizontalOptions="Center"
                    IconFilePath="{Binding Source={x:Reference ThisPage}, Path=IconEffect}"
                    Opacity="0.525"
                    Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
                    UseAssembly="{Binding {x:Reference ThisPage}}"
                    UseGradient="False"
                    VerticalOptions="Center"
                    WidthRequest="24" />


            </controls:TouchFrame>-->

            <!--  BUTTON BLACKEN BORDERS  -->
            <controls:TouchFrame
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_BlackenFrames"
                TimeLockDownMs="1000">
                <xam:FontIconLabel
                    Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
                    Style="{StaticResource CameraIcon}"
                    Text="{x:Static xam:FaPro.Expand}" />
            </controls:TouchFrame>

            <!--  BTN TAKE PHOTO  -->
            <controls:TouchFrame
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_CapturePhoto"
                TimeLockDownMs="1000">
                <xam:FontIconLabel
                    Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
                    Style="{StaticResource CameraIcon}"
                    Text="{x:Static xam:FaPro.Camera}">
                    <xam:FontIconLabel.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference Camera}, Path=IsTakingPhoto}"
                            TargetType="xam:FontIconLabel"
                            Value="True">
                            <Setter Property="TextColor" Value="#88883333" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference Camera}, Path=IsTakingPhoto}"
                            TargetType="xam:FontIconLabel"
                            Value="False">
                            <Setter Property="TextColor" Value="#88000000" />
                        </DataTrigger>
                    </xam:FontIconLabel.Triggers>
                </xam:FontIconLabel>
                <controls:TouchFrame.Triggers>
                    <DataTrigger
                        Binding="{Binding Source={x:Reference Camera}, Path=IsTakingPhoto}"
                        TargetType="controls:TouchFrame"
                        Value="True">
                        <Setter Property="InputTransparent" Value="True" />
                    </DataTrigger>
                    <DataTrigger
                        Binding="{Binding Source={x:Reference Camera}, Path=IsTakingPhoto}"
                        TargetType="controls:TouchFrame"
                        Value="False">
                        <Setter Property="InputTransparent" Value="False" />
                    </DataTrigger>
                </controls:TouchFrame.Triggers>
            </controls:TouchFrame>


        </StackLayout>

        <!--  SETTINGS  -->

        <!--  ADJUST  +/-  -->
        <StackLayout
            Margin="20,0,20,0"
            HorizontalOptions="End"
            IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings}"
            Spacing="30"
            VerticalOptions="Center">

            <!--  PLUS  -->
            <controls:TouchFrame
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_Plus"
                TimeLockDownMs="1000">
                <xam:FontIconLabel
                    Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                    Style="{StaticResource CameraIcon}"
                    Text="{x:Static xam:FaPro.Plus}" />
            </controls:TouchFrame>

            <!--  MINUS  -->
            <controls:TouchFrame
                Style="{StaticResource CameraButton}"
                Tapped="OnTapped_Minus"
                TimeLockDownMs="1000">
                <xam:FontIconLabel
                    Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                    Style="{StaticResource CameraIcon}"
                    Text="{x:Static xam:FaPro.Minus}" />
            </controls:TouchFrame>

        </StackLayout>


        <!--  BTN CLOSE  -->
        <controls:TouchFrame
            x:Name="BtnClose"
            Margin="0,0,20,30"
            HorizontalOptions="End"
            IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings}"
            Style="{StaticResource CameraButton}"
            Tapped="OnTapped_Close"
            TimeLockDownMs="1000"
            VerticalOptions="End">
            <xam:FontIconLabel
                Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                Style="{StaticResource CameraIcon}"
                Text="{x:Static xam:FaPro.Times}" />
        </controls:TouchFrame>

        <!--  BTN LENSES  -->
        <!--<controls:TouchFrame
            TimeLockDownMs ="1000"
            x:Name="BtnLenses"
            Margin="20,0,20,30"
            HorizontalOptions="Start"
            IsVisible="{Binding Source={x:Reference ThisPage}, Path=ShowSettings}"
            Style="{StaticResource CameraButton}"
            Tapped="OnTapped_Lenses"
            VerticalOptions="End">
            <xam:FontIconLabel
                Rotation="{Binding Source={x:Reference Camera}, Path=Orientation, Converter={x:StaticResource OrientationToRotationConverter}}"
                Style="{StaticResource CameraIcon}"
                Text="{x:Static xam:FaPro.Eye}" />
        </controls:TouchFrame>-->

        <!--  Preset hotspot  -->
        <controls1:SemiRotatedHotspot
            x:Name="HotspotPreset"
            Margin="10"
            d:BackgroundColor="#33ff0000"
            HeightRequest="40"
            HorizontalOptions="Start"
            Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
            Tapped="OnTapped_FocalPreset"
            VerticalOptions="Start"
            WidthRequest="120" />

        <!--  Focal hotspot  -->
        <controls1:SemiRotatedHotspot
            x:Name="HotspotFocal"
            Margin="10"
            d:BackgroundColor="#3300ff00"
            HeightRequest="40"
            HorizontalOptions="End"
            Rotation="{Binding Source={x:Reference Camera}, Path=SensorRotation}"
            Tapped="OnTapped_Focal"
            VerticalOptions="Start"
            WidthRequest="80" />

        <!--  PermissionsWarning  -->
        <Grid
            InputTransparent="True"
            IsVisible="{Binding Source={x:Reference ThisPage}, Path=PermissionsWarning}"
            VerticalOptions="FillAndExpand">

            <Label
                FontSize="15"
                HorizontalOptions="Center"
                InputTransparent="True"
                Text="{x:Static resX:ResStrings.NoPermissions}"
                TextColor="White"
                VerticalOptions="Center" />

        </Grid>

    </Grid>

</pages:IncludedContent>