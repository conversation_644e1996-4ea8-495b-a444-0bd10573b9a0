﻿<?xml version="1.0" encoding="utf-8" ?>
<popups:PopupPage
    x:Class="AppoMobi.Auth.WaitingPopup"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:pages="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:pages1="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:popups="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    Color="#00000000">


    <ContentPage.Content>

        <Grid
            Margin="0"
            Padding="0"
            BackgroundColor="{x:Static xam:BackColors.WaitingPopup}"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand">

            <ActivityIndicator
                x:Name="Spinner"
                HorizontalOptions="Center"
                IsRunning="True"
                VerticalOptions="Center"
                Color="{x:Static appoMobi:AppColors.AccentLight}" />

            <Label
                x:Name="ControlMessage"
                FontAttributes="Bold"
                FontSize="14"
                IsVisible="False"
                Text="{Binding WaitDesc}"
                TextColor="{x:Static appoMobi:AppColors.AccentLight}"
                VerticalOptions="Center" />

        </Grid>




    </ContentPage.Content>

</popups:PopupPage>