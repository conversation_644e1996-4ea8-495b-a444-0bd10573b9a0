﻿<?xml version="1.0" encoding="UTF-8" ?>
<Grid
    x:Class="AppoMobi.UI.TimeCalculator"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:models="clr-namespace:AppoMobi.Models"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:tenant="clr-namespace:AppoMobi.Tenant"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:DataType="ui:TimeCalculator"
    HorizontalOptions="FillAndExpand"
    RowSpacing="0"
    VerticalOptions="FillAndExpand">

    <Grid.RowDefinitions>
        <RowDefinition Height="*" />
        <RowDefinition Height="Auto" />
    </Grid.RowDefinitions>

    <!--  row 0  -->

    <!--  history scroll  -->
    <ScrollView
        x:Name="HistoryScroll"
        Margin="0,0,16,0"
        HorizontalOptions="End"
        InputTransparent="False"
        Rotation="180"
        VerticalOptions="Fill">

        <xam:NiftyDataStack
            x:Name="cHistoryList"
            ItemsSource="{Binding History}"
            VerticalOptions="Start">

            <xam:NiftyDataStack.ItemTemplate>
                <DataTemplate x:DataType="models:CalcTimeHistoryEntry">
                    <xam:NiftyCell HorizontalOptions="Fill" Rotation="180">

                        <Label
                            FontSize="{x:Static tenant:TenantOptions.FontSizeCalcHistory}"
                            HorizontalOptions="Fill"
                            HorizontalTextAlignment="End"
                            LineBreakMode="NoWrap"
                            MaxLines="1"
                            Text="{Binding TimeDesc}"
                            TextColor="Gray" />

                    </xam:NiftyCell>
                </DataTemplate>
            </xam:NiftyDataStack.ItemTemplate>

        </xam:NiftyDataStack>
    </ScrollView>
    <!--<Label HorizontalOptions="End"
            x:Name="cHistoryDisplay"
            Margin="0,0,8,0"
            LineBreakMode="NoWrap"
            Text="00h 00m 00s"
            FontSize="{x:Static tenant:TenantOptions.FontSizeCalcHistory}" TextColor="Gray"/>-->



    <!--  row 1 keypad  -->
    <StackLayout
        x:Name="cMainStack"
        Grid.Row="1"
        Spacing="0">

        <!--  display  -->
        <Grid
            BackgroundColor="{x:Static xam:BackColors.Page}"
            HorizontalOptions="FillAndExpand"
            RowSpacing="0"
            VerticalOptions="FillAndExpand">


            <!--  operator  -->
            <Label
                x:Name="cOperator"
                Margin="8,0,0,44"
                FontSize="{x:Static tenant:TenantOptions.FontSizeCalcOperatorIndicator}"
                Text=" "
                TextColor="DimGray"
                VerticalOptions="End" />

            <!--  time entry fields  -->
            <Grid
              
                Margin="0,0,4,0"
                ColumnSpacing="2"
                HorizontalOptions="End"
                RowSpacing="8"
                VerticalOptions="Center">

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="27" />
                    <ColumnDefinition Width="60" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="60" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="60" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  sign maybe "-"  -->
                <ui:CalcTimeEntrySeparator
                    x:Name="cSign"
                    Grid.Column="0"
                    IsVisible="False"
                    Text="━"
                    TextColor="White"
                    TranslationY="-8" />

                <!--  row 0 entryes  -->
                <ui:CalcTimeEntry
                    x:Name="cHH"
                    Grid.Column="1"
                    BackgroundColor="Yellow"
                    Selected="OnEnteringTime"
                    StyleId="HH"
                    Tag="HH" />
                <ui:CalcTimeEntrySeparator
                    x:Name="cSeparatorHH"
                    Grid.Column="2"
                    Text="{x:Static resX:ResStrings.TimeCalculator_Hour}" />
                <ui:CalcTimeEntry
                    x:Name="cMM"
                    Grid.Column="3"
                    Selected="OnEnteringTime"
                    StyleId="MM"
                    Tag="MM" />
                <ui:CalcTimeEntrySeparator
                    x:Name="cSeparatorMM"
                    Grid.Column="4"
                    Text="{x:Static resX:ResStrings.TimeCalculator_Min}" />
                <ui:CalcTimeEntry
                    x:Name="cSS"
                    BackgroundColor="Red"
                    Grid.Column="5"
                    Selected="OnEnteringTime"
                    StyleId="SS"
                    Tag="SS" />
                <ui:CalcTimeEntrySeparator
                    x:Name="cSeparatorSS"
                    Grid.Column="6"
                    Text="{x:Static resX:ResStrings.TimeCalculator_Sec}" />

                <!--  wtf is this  -->
                <ui:CalcSimpleEntry
                    x:Name="cSimpleEntry"
                    Grid.Column="1"
                    Grid.ColumnSpan="6"
                    Margin="0,0,8,0"
                    BackgroundColor="Red" />


                <!--  row 1 ms display under entries - display MS -->
                <Label
                    x:Name="cSecondaryDisplay"
                    Grid.Row="1"
                    Grid.Column="1"
                    Grid.ColumnSpan="6"
                    Margin="0,0,8,8"
                    FontSize="{x:Static tenant:TenantOptions.FontSizeCalcSecondaryDisplay}"
                    HorizontalOptions="End"
                    LineBreakMode="NoWrap"
                    Text="{Binding TotalTimeMsDesc}"
                    TextColor="Gray" />

                <!--  total time result  -->
                <!--
          <Label
              Margin="8,0,0,8"
              HorizontalOptions="Start" LineBreakMode="NoWrap"
              FontSize="{x:Static tenant:TenantOptions.FontSizeCalcSecondaryDisplay}" Text="{Binding TotalTimeDesc}" TextColor="Gray"
              Grid.Row="1"
              Grid.Column="1"
              Grid.ColumnSpan="6"/>-->

            </Grid>

            <!--  total time result  -->
            <Label
                x:Name="cTotalMini"
                Margin="8,0,0,8"
                FontSize="{x:Static tenant:TenantOptions.FontSizeCalcSecondaryDisplay}"
                LineBreakMode="NoWrap"
                Text="{Binding TotalTimeDesc}"
                TextColor="Gray"
                VerticalOptions="End" />

        </Grid>


        <!--  keypad  -->
        <Grid
            Margin="0,5,0,0"
            Padding="5,0"
            ColumnSpacing="5"
            RowSpacing="5">

            <Grid.RowDefinitions>
                <RowDefinition Height="1" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="25*" />
                <ColumnDefinition Width="25*" />
                <ColumnDefinition Width="25*" />
                <ColumnDefinition Width="25*" />
            </Grid.ColumnDefinitions>

            <Label
                x:Name="resultText"
                Grid.ColumnSpan="4"
                BackgroundColor="{x:Static xam:BackColors.Page}"
                FontAttributes="Bold"
                FontSize="48"
                HorizontalTextAlignment="End"
                IsVisible="False"
                LineBreakMode="NoWrap"
                Text="0"
                TextColor="White"
                VerticalTextAlignment="Center" />

            <ui:CalcButton
                Grid.Row="1"
                Grid.Column="0"
                ButtonType="Clear"
                Clicked="OnClear"
                Text="C" />
            <ui:CalcButton
                x:Name="btnTimer"
                Grid.Row="1"
                Grid.Column="1"
                ButtonType="Clear"
                Clicked="OnTimer"
                FontSize="36"
                Text="T" />
            <ui:CalcButton
                Grid.Row="1"
                Grid.Column="2"
                ButtonType="Other"
                Clicked="OnPressedOperator"
                Tag="%"
                Text="%" />




            <ui:CalcButton
                Grid.Row="2"
                Grid.Column="0"
                Clicked="OnPressedNumber"
                Text="7" />
            <ui:CalcButton
                Grid.Row="2"
                Grid.Column="1"
                Clicked="OnPressedNumber"
                Text="8" />
            <ui:CalcButton
                Grid.Row="2"
                Grid.Column="2"
                Clicked="OnPressedNumber"
                Text="9" />

            <ui:CalcButton
                Grid.Row="3"
                Grid.Column="0"
                Clicked="OnPressedNumber"
                Text="4" />

            <ui:CalcButton
                Grid.Row="3"
                Grid.Column="1"
                Clicked="OnPressedNumber"
                Text="5" />
            <ui:CalcButton
                Grid.Row="3"
                Grid.Column="2"
                Clicked="OnPressedNumber"
                Text="6" />

            <ui:CalcButton
                Grid.Row="4"
                Grid.Column="0"
                Clicked="OnPressedNumber"
                Text="1" />
            <ui:CalcButton
                Grid.Row="4"
                Grid.Column="1"
                Clicked="OnPressedNumber"
                Text="2" />
            <ui:CalcButton
                Grid.Row="4"
                Grid.Column="2"
                Clicked="OnPressedNumber"
                Text="3" />

            <ui:CalcButton
                Grid.Row="5"
                Grid.Column="0"
                Clicked="OnPressedNumber"
                Text="0" />
            <ui:CalcButton
                Grid.Row="5"
                Grid.Column="1"
                Clicked="OnPressedNumber"
                Text="{x:Static resX:ResStrings.NumericDoubleDot}" />

            <!--<ui:CalcButton Text="x2" Grid.Row="5" Grid.Column="2"   Clicked="squareclicked"  />-->

            <ui:CalcButton
                Grid.Row="5"
                Grid.Column="2"
                Grid.ColumnSpan="2"
                ButtonType="Operator"
                Clicked="OnPressedOperator"
                Tag="="
                Text="=" />
            <ui:CalcButton
                Grid.Row="1"
                Grid.Column="3"
                ButtonType="Operator"
                Clicked="OnPressedOperator"
                Tag="÷"
                Text="÷" />
            <ui:CalcButton
                Grid.Row="2"
                Grid.Column="3"
                ButtonType="Operator"
                Clicked="OnPressedOperator"
                Tag="×"
                Text="×" />
            <ui:CalcButton
                Grid.Row="3"
                Grid.Column="3"
                ButtonType="Operator"
                Clicked="OnPressedOperator"
                Tag="-"
                Text="-" />
            <ui:CalcButton
                Grid.Row="4"
                Grid.Column="3"
                ButtonType="Operator"
                Clicked="OnPressedOperator"
                Tag="+"
                Text="+" />

        </Grid>

        <BoxView
            BackgroundColor="Transparent"
            x:Name="bottomPaddingFullscreen"
            HorizontalOptions="Fill"
            VerticalOptions="Start" />

    </StackLayout>

</Grid>