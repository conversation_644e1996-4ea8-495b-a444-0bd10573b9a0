﻿using AppoMobi.Forms.Controls.Input;
using AppoMobi.iOS;
using CoreGraphics;
using Foundation;
using Microsoft.Maui.Controls.Compatibility.Platform.iOS;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using System;
using System.ComponentModel;
using UIKit;


// HandlerEntryCentered
//[assembly: ExportRenderer(typeof(EntryCentered), typeof(HandlerEntryCentered))]
namespace AppoMobi.Handlers
{
    public partial class HandlerEntryCenteredApple : EntryHandler
    {
        private UIColor _cursorColor;
        protected EntryCentered FormsControl { get; private set; }
 

        protected override void ConnectHandler(MauiTextField platformView)
        {
            base.ConnectHandler(platformView);

            this.FormsControl = (EntryCentered)this.VirtualView;

            UpdateSkin(); //set color bindings etc

            FormsControl.RendererNeedUpdate += OnRendererNeedUpdate;

            var nativeTextField = (UITextField)PlatformView;

            PlatformView.ShouldChangeCharacters = CustomFilter;

            UpdateSkin();

            // do whatever you want to the UITextField here!

            //````````````````````````````````````````````````
            // Focused
            //````````````````````````````````````````````````
            nativeTextField.EditingDidBegin += OnEditingDidBegin;


            //````````````````````````````````````````````````
            // Unfocused
            //````````````````````````````````````````````````
            nativeTextField.EditingDidEnd += EditingDidEnd;


            nativeTextField.EditingDidBegin += EditingDidBegin;

            nativeTextField.EditingChanged += EditingChanged;

        }

        protected override void DisconnectHandler(MauiTextField platformView)
        {
            System.Diagnostics.Debug.WriteLine("EntryCentered disconnecting handler..");
            Release();

            base.DisconnectHandler(platformView);
        }

        private void Release()
        {
            FormsControl.RendererNeedUpdate -= OnRendererNeedUpdate;

            try
            {
                if (PlatformView != null)
                {
                    PlatformView.EditingDidBegin -= OnEditingDidBegin;
                    PlatformView.EditingChanged -= EditingChanged;
                    PlatformView.EditingDidEnd -= EditingDidEnd;
                    PlatformView.EditingDidBegin -= EditingDidBegin;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"[WTF(!)] {e}");
            }

            FormsControl = null;
        }

        private void OnRendererNeedUpdate(object sender, EventArgs e)
        {
            UpdateSkin();
        }

        private void UpdateSkin()
        {
            try
            {
                if (PlatformView == null || FormsControl == null)
                    return;

                //_cursorColor = FormsControl.CursorColor.ToPlatform();
                //PlatformView.TintColor = _cursorColor; //tint cursor

                if (FormsControl.TextColor!=null)
                    PlatformView.TintColor = FormsControl.TextColor.ToPlatform();

                PlatformView.TextAlignment = (FormsControl.Centered ||
                                         (string.IsNullOrEmpty(FormsControl.Text) && FormsControl.PlaceHolderCentered)) ? UITextAlignment.Center : UITextAlignment.Left;

                //todo placeholder size
                if (string.IsNullOrEmpty(FormsControl.Text))
                {

                }
                else
                {

                }

                PlatformView.BorderStyle = UITextBorderStyle.None;

                PlatformView.LeftView = new UIView(new CGRect(0, 0, 2, PlatformView.Frame.Height));
                PlatformView.RightView = new UIView(new CGRect(0, 0, 2, PlatformView.Frame.Height));
                PlatformView.LeftViewMode = UITextFieldViewMode.Always;
                PlatformView.RightViewMode = UITextFieldViewMode.Always;

                //PlatformView.BackgroundColor = UIColor.FromRGBA(0, 0, 0, 0);

                //PlatformView.LayoutMargins = new UIEdgeInsets(6,0,0,0);

                /*
                            var LetterSpacing = TextFont.Info.LetterSpacing;

                            //ffs not working
                            NSObject value = NSObject.FromObject(LetterSpacing * 10);
                            NSString key = new NSString("NSKern");
                            nativeTextField.WeakDefaultTextAttributes.SetValueForKey(value, key);

                            SetFonts();
                            */

                PlatformView.ClipsToBounds = true;
                PlatformView.Layer.MasksToBounds = true;
                PlatformView.Layer.CornerRadius = 4; // This is for rounded corners. TODO
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

        }

        //---------------------------------------------------------------------------------------------------------
        private bool CustomFilter(UITextField textfield, NSRange range, string replacementstring)
        //---------------------------------------------------------------------------------------------------------
        {


            if (FormsControl.InputFilter != null)
            {
                var output = FormsControl.InputFilter(replacementstring, (int)range.Location, (int)range.Length);
                if (output == replacementstring)
                    return true;
                //if (string.IsNullOrEmpty(output)) 
                return false;
            }
            return true;
        }

        void OnEditingDidBegin(object sender, EventArgs eIos)
        {
            PlatformView.TintColor = _cursorColor; //tint cursor
            FormsControl.OnFocused();
        }




        private void EditingDidEnd(object sender, EventArgs e)
        {
            FormsControl.OnUnfocused();
        }

        private void EditingDidBegin(object sender, EventArgs e)
        {
            if (FormsControl.SelectOnFocus)
                PlatformView.PerformSelector(new ObjCRuntime.Selector("selectAll"), null, 0.0f);
        }

        private void EditingChanged(object sender, EventArgs e)
        {
            //nativeTextField.Text = nativeTextField.Text.ToUpper();

            //var text = PlatformView.Text;

            FormsControl.OnEditing();
            //if (text == null) return;


            //add formatting
            //var attributedString = new NSMutableAttributedString(text);
            //var paragraphStyle = new NSMutableParagraphStyle();
            //paragraphStyle.MaximumLineHeight = PlatformView.Font.Ascender + 0.8f;
            //if (FormsControl.HorizontalOptions.Alignment == LayoutAlignment.Center)
            //    paragraphStyle.Alignment = UITextAlignment.Center;
            //attributedString.AddAttribute(UIStringAttributeKey.ParagraphStyle, paragraphStyle, range);
            //var nsKern = new NSString("NSKern");
            //var spacing = NSObject.FromObject(LetterSpacing * 10);
            //attributedString.AddAttribute(nsKern, spacing, range);
            //nativeTextField.AttributedText = attributedString;
            UpdateSkin();
        }

        protected override bool OnShouldReturn(UITextField view)
        {
            if (FormsControl.UnfocusLocked)
            {
                FormsControl.SendCompleted();
                return false;
            }

            return base.OnShouldReturn(view);
        }


        /*
        
        private void SetFonts()
        
        {
            var LetterSpacing = TextFont.Info.LetterSpacing;


            //set font
            try
            {
                var font = UIFont.FromName(TextFont.Normal, (float)FormsControl.TextSize);
                PlatformView.Font = font;

                //set placeholder char spacing
                //add formatting
                var placeholder = PlatformView.Placeholder;
                if (placeholder != null)
                {
                    var range = new NSRange(0, placeholder.Length);
                    var placeholderFont = UIFont.FromName(TextFont.Normal, (float)FormsControl.PlaceholderTextSize);
                    var attributedString = new NSMutableAttributedString(placeholder, new UIStringAttributes()
                    {
                        ForegroundColor = FormsControl.PlaceholderColor.ToUIColor(),
                        Font = placeholderFont
                    });
                    //var attributedString = new NSMutableAttributedString(placeholder);
                    //var paragraphStyle = new NSMutableParagraphStyle();
                    //paragraphStyle.MaximumLineHeight = PlatformView.Font.Ascender + 0.8f;
                    //if (FormsControl.HorizontalOptions.Alignment == LayoutAlignment.Center)
                    //    paragraphStyle.Alignment = UITextAlignment.Center;
                    //attributedString.AddAttribute(UIStringAttributeKey.ParagraphStyle, paragraphStyle, range);
                    var nsKern = new NSString("NSKern");
                    var spacing = NSObject.FromObject(LetterSpacing * 10);
                    attributedString.AddAttribute(nsKern, spacing, range);
                    //                    nativeTextField.AttributedText = attributedString;
                    PlatformView.AttributedPlaceholder = attributedString;
                }
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
            }

        }
        */


    }

    //droid
    //public class FormsControlRenderer : EntryRenderer
    //{
    //    protected override void OnElementChanged(ElementChangedEventArgs<Entry> e)
    //    {
    //        base.OnElementChanged(e);
    //        if (e.OldElement == null)
    //        {
    //            var nativeEditText = (global::Android.Widget.EditText)PlatformView;
    //            nativeEditText.SetSelectAllOnFocus(true);
    //        }
    //    }
    //}



    //UWP
    /*
     [assembly: ExportRenderer(typeof(MyNamespace.FormsControl), typeof(MyNamespace.WinPhone.FormsControlRenderer))]
namespace MyNamespace.WinPhone
{
    public class FormsControlRenderer : EntryRenderer
    {
        protected override void OnElementChanged(ElementChangedEventArgs<Entry> entry)
        {
            base.OnElementChanged(entry);
            if (PlatformView != null)
            {
                PlatformView.GotFocus += (object sender, Windows.UI.Xaml.RoutedEventArgs ea)=>
                {
                    PlatformView.SelectAll();
                };
            }
        }
    }
}
     * */
}

