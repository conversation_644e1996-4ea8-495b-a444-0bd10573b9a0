﻿using AppoMobi.Mobile.Import.Abstractions;
using AppoMobi.Mobile.Import.Enums;
using CommunityToolkit.Maui.Core;

using DrawnUi.Draw;
using Microsoft.Maui.Platform;
using Microsoft.UI.Xaml.Controls.Primitives;
using System.Diagnostics;


namespace AppoMobi.Mobile
{

    public partial class Toast : IToastMessage
    {
        public void ShortAlert(string message)
        {
            Trace.WriteLine(message);
        }

        public void LongAlert(string message, ToastPosition position)
        {
            Trace.WriteLine(message);
        }
    }
}