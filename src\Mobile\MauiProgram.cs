﻿global using AppoMobi.Framework;
global using AppoMobi.Services;
global using AppoMobi.Specials;
global using AppoMobi.Views;
global using AppoMobi.Mobile;
global using AppoMobi.Mobile.Import.Common.ResX;
global using AppoMobi.Mobile.Views.Navigation;
global using DrawnUi.Draw;
global using DrawnUi.Draw;
global using SkiaSharp;
global using SkiaSharp.Views.Maui;
global using SkiaSharp.Views.Maui.Controls;
global using System.Runtime.CompilerServices;

global using DrawnUi.Camera;

using AppoMobi;
using AppoMobi.Forms.Content.Camera.Controls;
using AppoMobi.Forms.Controls.Input;
using AppoMobi.Handlers;
using AppoMobi.Framework.Maui.Interfaces;
using AppoMobi.Pages;
using AppoMobi.Xam;
using CommunityToolkit.Maui;
using CommunityToolkit.Maui.Core.Handlers;
using DrawnUi.Draw;
using FFImageLoading.Maui;
using Microsoft.Extensions.Logging;
using org.mariuszgromada.math.mxparser;

#if ANDROID
using AppoMobi.Droid.Renderers;
using AppoMobi.Droid;
using AppoMobi.Droid.Camera;
using AppoMobi.WheelsForms;
#endif

namespace AppoMobi.Mobile
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            License.iConfirmNonCommercialUse("John Doe"); //MathParser

            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseMauiCommunityToolkit()
                .UseDrawnUi(new()
                {
                    MobileIsFullscreen = true,
                    DesktopWindow = new()
                    {
                        Height = 812, Width = 375,
                        //IsFixedSize = true
                    }
                })
                .UseFFImageLoading()
                .ConfigureMauiHandlers(handlers =>
                {
                    PopupHandler.PopUpCommandMapper.Add("Custom", (h, v) =>
                    {
                        if (v is Microsoft.Maui.Controls.Page page)
                        {
                            Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific.Page.SetUseSafeArea(page, false);
                        }
                    });

#if ANDROID
                    PlatformExtensions.ConfigureHandlers(handlers);

                    //todo in process of debugging slow muaui rendering
                    //Microsoft.Maui.Handlers.LayoutHandler.CommandMapper
                    //    .AppendToMapping(nameof(IView.Frame), (handler, view, arg) =>
                    //{
                    //    if (view is IncludedContent control)
                    //    {
                    //        Super.Log($"[IncludedContent] {control.GetType().Name} Frame changed {arg}");
                    //    }
                    //});

#elif IOS
                        handlers.AddHandler(typeof(CameraPreview), typeof(AppoMobi.iOS.Camera.CameraPreviewRenderer));
                        handlers.AddHandler(typeof(EntryCentered), typeof(HandlerEntryCenteredApple));

#elif MACCATALYST
#elif WINDOWS
                        handlers.AddHandler(typeof(CameraPreview), typeof(AppoMobi.Mobile.CameraViewPreviewRenderer));
#endif
                })
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("AvenirNextCyr-Regular.ttf", "ui");
                    fonts.AddFont("AvenirNextCyr-Bold.ttf", "FontTextBold");
                    fonts.AddFont("AvenirNextCyr-Regular.ttf", "FontText");
                    fonts.AddFont("AvenirNextCyr-Medium.ttf", "FontTextTitle");
                    fonts.AddFont("AvenirNextCyr-Thin.ttf", "FontTextLight");

                    fonts.AddFont("fa-regular-400.ttf", "Fa");
                    fonts.AddFont("fa-light-300.ttf", "FaLight");
                    fonts.AddFont("fa-solid-900.ttf", "FaSolid");
                    fonts.AddFont("fa-brands-400.ttf", "FaBrands");
                });

            SkiaFontManager.ThrowIfFailedToCreateFont = false;

#if DEBUG
            builder.Logging.AddDebug();
#endif

            //APP INFRASTRUCTURE
            builder.Services.AddSingleton<IAppStorage, AppStorage>();
            builder.Services.AddSingleton<IUIAssistant, UIAssistant>();
            builder.Services.AddSingleton<IInAppMessager, InAppMessager>();
            builder.Services.AddSingleton<NavigationViewModel>();

            builder.RegisterTransientShellRoutes();

            return builder.Build();
        }

        public static bool FixLayouts
        {
            get
            {
#if ANDROID || WINDOWS
                return false;
#endif
                return true;
            }
        }

        public static bool IsMobile
        {
            get { return DeviceInfo.Platform == DevicePlatform.Android || DeviceInfo.Platform == DevicePlatform.iOS; }
        }
    }
}
