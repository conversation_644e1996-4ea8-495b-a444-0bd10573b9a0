﻿using AnimatedNavi.Forms;
using AppoMobi.Common.Dto;
using AppoMobi.Maui.Navigation;
using AppoMobi.Models;
using AppoMobi.Services;
using AppoMobi.Tenant;
using AppoMobi.Touch;
using AppoMobi.UI;
using AppoMobi.Xam;
using AppoMobi.Xam.Antispam;
using FFImageLoading.Maui;
using FFImageLoading.Transformations;
using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Reflection;

//using Xamarin.Auth.OAuth2;

namespace AppoMobi.Pages
{
    public partial class PageEnhancedNav : EnhancedPage, IAnimationPage, IPageEnhancedNav
    {

        public IPageAnimation PageAnimation { get; set; } = new SlidePageAnimation()
        {
            Duration = AnimationDuration.Short,
            Subtype = AnimationSubtype.FromRight
        };

        public void OnAnimationFinished(bool isPopAnimation)
        {
        }

        public void OnAnimationStarted(bool isPopAnimation)
        {
        }

        private ContentView cLoading;

        public PageEnhancedNav()
        {
            //vm = new VenueViewModel();
            InitializeComponent();

            cLoading = new();

            BindingContext = this;

            cNavBar.Title = Title;

            var prop1 = GetType();
            var pp1 = prop1;
            if (pp1 != null)
            {
                MyPageType = pp1.ToString();
            }

            //if (imgNavBarDefault == null)
            //{
            //    //var generatedFilename = Core.AssemblyName + ".Images.Brand." + "wptitlebar.jpg";
            //    //imgNavBarDefault = ImageSource.FromResource(generatedFilename);
            //    imgNavBarDefault = Core.ImageFromResources("Brand", "vague.jpg");
            //}

            //srcNavigationBack.Source = imgNavBarDefault;
            //WallpaperBackup = cWallpaperImage.Source;

            NavigationPage.SetHasNavigationBar(this, false);
            NavigationPage.SetBackButtonTitle(this, ResStrings.GoBack);

            InitControls();

            this.OnRotation += OnPageRotated;
            cNavBar.SearchExecute += OnSearchExecute;
            cNavBar.SearchChanged += OnSearchChanged;
            cNavBar.SearchCanceled += OnSearchCanceled;

            //============================================================================
            App.Instance.Messager.Subscribe<string>(this, "TabSelected", async (sender, arg) =>
            //============================================================================
            {

                var e = new MessageEventArgs();
                e.Message = arg;
                OnTabSelected(this, e);

            });

            //============================================================================
            App.Instance.Messager.Subscribe<string>(this, "NeedRestart", async (sender, arg) =>
            //============================================================================
            {
                ReleaseOnClosing();
                return;
            });

        }


        public async void ConnectionError()
        //-----------------------------------------------------------------
        {
            StopTransparentContentLoader();
            await Core.Current.Alert(ResStrings.ErrorConnection);
            ReplaceContent(typeof(ContentDisconnected));
        }


        //-----------------------------------------------------------------
        protected virtual void SetGoBackNabIcon()
        //-----------------------------------------------------------------
        {
            if (AlwaysShowMenu)
            {
                SetDrawerMenuIcon();
                cNavBar.LeftIcon2Symbol.SetIcon(FontIcons.fa_long_arrow_left, "FaLight");
                cNavBar.ToggleButtonVisibility(ButtonType.Left2, true);
                //cOverlayNavBar.LeftIcon1Symbol.SetIcon(FontIcons.fa_long_arrow_left, "FaLight");
            }
            else
            {
                cNavBar.LeftIcon1Symbol.SetIcon(FontIcons.fa_long_arrow_left, "FaLight");
            }
            cOverlayNavBar.LeftIcon1Symbol.SetIcon(FontIcons.fa_long_arrow_left, "FaLight");
        }

        //-----------------------------------------------------------------
        protected virtual void SetDrawerMenuIcon()
        //-----------------------------------------------------------------
        {
            cNavBar.LeftIcon1Symbol.SetIcon(FontIcons.fa_bars);
            cOverlayNavBar.LeftIcon1Symbol.SetIcon(FontIcons.fa_bars, "FaLight");
        }

        public bool Appeared { get; protected set; }

        //---------------------------------------------------------
        protected override void OnAppearing()
        //---------------------------------------------------------
        {
            Appeared = true;

            if (Core.IsIOS && !onceInit)
            {
                onceInit = true;
                InitControls();

            }
            else
            {
                onceInit = true;

                //cTabsShadow.IsVisible = IsInTabs;
            }

            Debug.WriteLine($"[ONAPPEARING] {Title}");


            if (!MenuInit)
            {
                cOverlayScrollUp.LeftIcon1Symbol.SetIcon(FontIcons.fa_arrow_to_top, "FaLight");
                // ToggleButtonVisibility(ButtonType.Left2, false);

                //Can go back and is Modal
                if ((IsChildInNavigationStack(IsModal) || IsModal) && !PseudoTab) //never show menu for modal
                {
                    ShowGoBack = true;
                    SetGoBackNabIcon();
                }
                else
                if (IsChildInNavigationStack() && !PseudoTab)
                {
                    ShowGoBack = true;
                    SetGoBackNabIcon();

                }
                else
                {
                    //drawer menu
                    ShowGoBack = false;
                    SetDrawerMenuIcon();
                }
                MenuInit = true;
            }

            //  if (Core.IsIOS)

            //ProcessScrollingState();

            //UpdateNavScroll(MainScroll.ScrollY);

            //this hacky shit is needed to refresh buggy buttons background images
            // btn1.Refresh();



            //OnActivating
            if (Core.IsIOS)
            {
                Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                {
                    OnPageActivated();
                    // Don't repeat the timer 
                    return false;
                });
            }
            else
                OnPageActivated();



            base.OnAppearing();

            if (DeviceInfo.Platform == DevicePlatform.Android)
                Globals.Values.CurrentPage = this;

            if (TimesAppeared == 0 && Core.IsIOS)
            {
                Device.StartTimer(TimeSpan.FromSeconds(0.1), () =>
                {
                    AnimatingPage();
                    // Don't repeat the timer 
                    return false;
                });
            }
            else
                AnimatingPage();

            TimesAppeared++;
            Debug.WriteLine($"Page appeared. Content.Width: {cContent.Width}, FullScreen.Width: {cFullScreen.Width}");


            //NEW send to content
            IIncludedContent content = GetContent();
            content?.OnAppearing();
        }


        #region ContentCommands

        public virtual void CommandFromContentRecieved(string command, string param)

        {
        }

        public bool CommandFromContent(string command, string param)

        {
            var content = (IIncludedContent)cContent.Content;
            if (IsFullScreen)
                content = (IIncludedContent)cFullScreen.Content;
            if (content == null) return false;

            CommandFromContentRecieved(command, param); //send to self
            return true;
        }

        public bool CommandToContent(string command, string param)

        {
            IIncludedContent content = (IIncludedContent)cContent.Content;
            if (IsFullScreen)
                content = (IIncludedContent)cFullScreen.Content;
            if (content == null) return false;

            content.CommandToContentRecieved(command, param);
            return true;
        }
        #endregion



        //new

        public void DarkenWallpaper(double howMuch)

        {
            cWallpaperImage.Darken = howMuch;
        }

        //todo add blur

        //popup code
        #region PopupCode


        public virtual void CommandFromPopupRecieved(string command, string param)
        {
        }


        public bool CommandFromPopup(string command, string param)
        {
            IIncludedContent content = (IIncludedContent)cContent.Content;
            if (IsFullScreen)
                content = (IIncludedContent)cFullScreen.Content;
            if (content == null) return false;

            content.CommandFromPopupRecieved(command, param);//send to content
            CommandFromPopupRecieved(command, param); //send to self
            return true;
        }

        public bool CommandToPopup(string command, string param)
        {
            IncludedPopup popup = (IncludedPopup)cPopup.Content;
            if (popup == null) return false;
            popup.CommandToPopupRecieved(command, param);
            return true;
        }


        public void LayoutPopup(LayoutOptions horizontalOptions, LayoutOptions verticalOptions)

        {
            cPopup.HorizontalOptions = horizontalOptions;
            cPopup.VerticalOptions = verticalOptions;
        }

        public void ShowPopup(bool visible = true)

        {
            cPopup.IsVisible = visible;
        }

        public void HidePopup()

        {
            cPopup.IsVisible = false;
        }

        //public ContentView Popup()
        //{
        //    return cPopup;
        //}        
        #endregion


        private ImageSource WallpaperBackup { get; set; }

        private static ImageSource imgNavBarDefault { get; set; }

        public string MyPageType { get; set; }

        public int TimesAppeared { get; private set; }


        public virtual void AnimatingPage()

        {
            IIncludedContent content = (IIncludedContent)cContent.Content;
            if (IsFullScreen)
                content = (IIncludedContent)cFullScreen.Content;
            if (content == null) return;

            content.AnimatingPage();//send to content
        }


        public virtual void PreparingPageAnimations()

        {
            IIncludedContent content = (IIncludedContent)cContent.Content;
            if (IsFullScreen)
                content = (IIncludedContent)cFullScreen.Content;
            if (content == null) return;

            content.PreparingPageAnimations();//send to content
        }

        public bool GoBackLocked { get; set; }


        public virtual async Task<bool> OnBeforeGoBack(bool cannotGoBack)
        {
            CanNotGoBack = cannotGoBack;

            IIncludedContent content = GetContent();
            content?.OnBeforeGoBack();

            return CanNotGoBack;
        }

        private bool CanNotGoBack { get; set; }


        public async Task GoBack()

        {
            await OnBeforeGoBack(false);
            if (CanNotGoBack) return;

            if (Navigation.NavigationStack.Count > 1 || IsModal)
            {
                Popping?.Invoke(this, new NavigationEventArgs(this));
                await NavigateBack(); //ReleaseOnClosing called inside there
            }
            else
                ShowMenu();
        }

        public async Task Destroy()
        {
            await OnBeforeGoBack(false);
            ReleaseOnClosing();
        }


        public async void HandlerGoBack(object sender, EventArgs e)

        {
            await GoBack();
        }




        protected override bool OnBackButtonPressed()

        {
            bool exitApp = false;
            //todo need check for if we are inside tabs!!!
            var t = 2;
            if (IsInTabs) t = 1;

            var stack = Navigation.NavigationStack;
            if (IsModal)
            {
                stack = Navigation.ModalStack;
                t = 1;
            }

            if (stack.Count < t)
            {
                if (maybe_exit)
                {
                    //call shit

                    //exit app
                    return false;
                }
                Toast.ShortMessage(ResStrings.PressBACKOnceAgain);
                maybe_exit = true;
                Device.StartTimer(TimeSpan.FromSeconds(2), () =>
                {
                    maybe_exit = false;
                    // false - Don't repeat the timer 
                    return false;
                });
                return true; //stay here
            }

            //manual stuff
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var cannotGoBack = await OnBeforeGoBack(false);
                // Update the UI
                if (cannotGoBack) return;
                if (Navigation.NavigationStack.Count > 1 || IsModal)
                {
                    Popping?.Invoke(this, new NavigationEventArgs(this));
                    await NavigateBack(); //ReleaseOnClosing called inside there

                }
                //else
                //    ShowMenu();
            });

            return true; //do not exit        
        }

        private bool maybe_exit { get; set; } = false;

        public EventHandler<NavigationEventArgs> Popping { get; set; }


        public override void OnDisposing()
        {
            //NEW send to content
            IIncludedContent content = GetContent();
            content?.Dispose();

            base.OnDisposing();
        }


        public override void OnTabActivated()
        {
            //NEW send to content
            IIncludedContent content = GetContent();
            content?.OnTabActivated();

            base.OnTabActivated();
        }

        public override void OnTabDeactivated()
        {
            //NEW send to content
            IIncludedContent content = GetContent();
            content?.OnTabDeactivated();

            base.OnTabDeactivated();
        }


        protected override void OnDisappearing()

        {
            Appeared = false;

            base.OnDisappearing();

            PreparingPageAnimations();

            //NEW send to content
            IIncludedContent content = GetContent();
            content?.OnDisappearing();
        }
        //-----------------------------------------------------------------
        public IIncludedContent GetContent()
        //-----------------------------------------------------------------
        {
            IIncludedContent content = (IIncludedContent)cContent.Content;

            if (IsFullScreen)
                content = (IIncludedContent)cFullScreen.Content;

            if (content == null)
            {
                content = InsertContent;
            }

            return content;
        }

        public View GetContainer()
        {
            return MainGrid;
        }

        public override void ReleaseOnClosing()

        {
            App.Instance.Messager.Unsubscribe(this, "TabSelected");
            App.Instance.Messager.Unsubscribe(this, "NeedRestart");

            this.OnRotation -= OnPageRotated;
            cNavBar.SearchExecute -= OnSearchExecute;
            cNavBar.SearchChanged -= OnSearchChanged;
            cNavBar.SearchCanceled -= OnSearchCanceled;


            if (BindingContext != null)
            {
                INotifyPropertyChanged ctx = null;
                try
                {
                    _oldBindingContext = (INotifyPropertyChanged)BindingContext;
                    ctx = (INotifyPropertyChanged)BindingContext;
                    ctx.PropertyChanged -= OnBindingContextPropertyChanged;
                }
                catch
                {
                }
            }

            if (InsertContent != null)
            {
                InsertContent?.OnTabDeactivated();
                //try dispose
                InsertContent.Dispose();
            }

            if (InsertPopup != null)
            {
                //try dispose
                InsertPopup.Dispose();
            }

            base.ReleaseOnClosing();
        }



        public virtual void OnPageActivated()

        {
            return;
        }


        public virtual void OnTabSelected(object sender, MessageEventArgs e)

        {
            if (!string.IsNullOrEmpty(e?.Message))
            {
                if (e.Message.Contains(MyPageType))
                {
                    //it's me
                    if (Core.IsIOS)
                    {
                        Device.StartTimer(TimeSpan.FromSeconds(1), () =>
                        {
                            OnPageActivated();
                            // Don't repeat the timer 
                            return false;
                        });
                    }
                    else
                        OnPageActivated();

                }
                else
                {
                    //WTF IS THIS
                    //if (!IsRoot) 
                    //    NavigateBack();
                }
            }

            return;
        }


        public virtual void OnSearchChanged(object sender, MessageEventArgs e)

        {

            return;
        }


        public virtual void OnSearchCanceled(object sender, MessageEventArgs e)
        {

            return;
        }


        public virtual void OnSearchExecute(object sender, MessageEventArgs e)
        {

            return;
        }


        public void AnimateIcon()
        {
            cNavBar.AnimateIcon();
        }

        private bool lockrotating { get; set; }

        private void OnPageRotated(object sender, RotationEventArgs e)

        {
            if (lockrotating) return;
            lockrotating = true;

            OnPageWasRotated();

            lockrotating = false;
        }

        public async virtual void OnPageWasRotated()

        {
            //base
            UpdateControls();

            //NEW send to content
            IIncludedContent content = GetContent();
            content?.OnUpdatedControls();
        }


        //-------------------------------------------------------------
        // FullScreenConstrained
        //-------------------------------------------------------------
        private const string nameFullScreenConstrained = "FullScreenConstrained";
        public static readonly BindableProperty FullScreenConstrainedProperty = BindableProperty.Create(nameFullScreenConstrained, typeof(bool), typeof(PageEnhancedNav), true); //, BindingMode.TwoWay
        public bool FullScreenConstrained
        {
            get { return (bool)GetValue(FullScreenConstrainedProperty); }
            set { SetValue(FullScreenConstrainedProperty, value); }
        }



        private void UpdateControls()

        {
            //max width
            if (MaxWidth < 0)
            {
                cContent.MaximumWidthRequest = MaxWidth;
                if (FullScreenConstrained)
                    cFullScreen.MaximumWidthRequest = MaxWidth;
            }
            else
            {
                cContent.MaximumWidthRequest = MaxWidth;
                if (FullScreenConstrained)
                    cFullScreen.MaximumWidthRequest = MaxWidth;

            }
            /*
                        if (Width > 1 && MaxWidth>1)
                        {
                            cContent.WidthRequest = MaxWidth;
                            if (FullScreenConstrained)
                                cFullScreen.WidthRequest = MaxWidth;
                        }
                        else 
                        if (MaxWidth < 0)
                        {
                            cContent.WidthRequest = MaxWidth;
                            if (FullScreenConstrained)
                                cFullScreen.WidthRequest = MaxWidth;
                        }
                        */

            //update sub-controls
            var valStatusBar = cNavStatusBar.Update(Orientation);
            boxStatusBar.HeightRequest = valStatusBar;
            var valNavBar = cNavBar.Update(Orientation);
            boxNavBar.HeightRequest = valNavBar;
            cNavBarPadding.HeightRequest = valNavBar + valStatusBar;
            cNavBarPadding2.HeightRequest = valNavBar + valStatusBar;

            bottomPadding.HeightRequest = Super.Screen.BottomInset;

            if (Core.IsAndroid)//&& Core.AndroidAPI < 23)
            {
                imgNavigationBack.HeightRequest = cNavStatusBar.HeightRequest + cNavBar.HeightRequest;
            }

            cOverlayNavBar.Update(Orientation);
        }

        private bool onceInit;

        /// <summary>
        /// 
        /// </summary>
        //-----------------------------------------------------------------
        protected virtual void SetupWallpaper()
        //-----------------------------------------------------------------
        {
            ControlWallpaper.CustomImageId = Core.Current.Info.Wallpaper;
            ControlWallpaper.Darken = PageOptions.FadeWallpaper;//Core.Current.Info.FadeWallpaper;
        }

        public WallpaperImage ControlWallpaper { get; private set; }


        /// <summary>
        /// Call it once at startup
        /// </summary>

        private void InitControls()

        {
            InitContents();

            ToggleButtonVisibility(ButtonType.Left2, false);

            ControlWallpaper = cWallpaperImage;
            SetupWallpaper();

            CustomPatternId = Core.Current.Info.Pattern;
            NavigationBackgroundOpacity = 1.0 - Core.Current.Info.FadePattern;

            //    if (Core.IsAndroid && Core.AndroidAPI < 23)
            //     {                

            imgNavigationBack.HeightRequest = cNavStatusBar.HeightRequest + cNavBar.HeightRequest;
    
            //     var source = imgNavigationBack.Source;
            //    imgNavigationBack.Source = null;
            //    imgNavigationBack.Source = source;
            // srcNavigationBack.Aspect = Aspect.Fill;
            //      }

            if (Core.IsIOS)
            {
                ;
                //if (Settings.Current.iModel != "iPhone X")
                //{
                //    cNavStatusShadowed.IsVisible = true;
                //}
                //else
                //    cNavStatusShadowed.IsVisible = false; //iPhoneX

            }
            else
            {
                //android
                //if (IsInTabs)
                //    cTabsShadow.IsVisible = true;
            }

            ScrollToTop(false);


            UpdateControls();
            cNavBar.BindingContext = this;
        }


        public override void OnAddedToTabs()
        {
            //if (Core.IsAndroid)
            //{
            //    cTabsShadow.IsVisible = true;
            //}
        }


        private void UpdateNavScroll(double pos)

        {
            if (pos < 0)
                pos = 0;

            if (pos >= 0 && cNavBar.Height > 0 && MainScroll.Height > 0)
            {

                var k = 1;

                // var startHidingAt = Container.Height/k;

                // if (Core.IsIOS) pos = -pos;
                if (pos > MainScroll.Height) pos = MainScroll.Height;
                var scrolledAmount = Math.Abs(1 - MainScroll.Height / (MainScroll.Height - pos));
                var translation = -cNavBar.Height * scrolledAmount;
                //if (translation > 0) translation = 0;

                var opacity = 1.0;

                if (translation <= 0)
                {
                    //scrolling out
                    cNavBar.TranslationY = translation;
                    boxNavBar.TranslationY = translation;
                    opacity = 2 - Math.Abs((1 - translation / cNavBar.Height));
                }
                else
                {
                    //display full
                    cNavBar.TranslationY = 0;
                    boxNavBar.TranslationY = 0;
                }

                if (translation >= -cNavBar.Height)
                {
                    //in place or bounced a bit more
                    imgNavigationBack.TranslationY = translation;
                }
                else
                {
                    //scrolling out of the screen
                    imgNavigationBack.TranslationY = -cNavBar.Height;
                }

                gridNavi.Opacity = opacity;

                if (Math.Abs(translation) > cNavBar.Height)
                    cOverlayNavBar.IsVisible = true;
                else
                {
                    cOverlayNavBar.IsVisible = false;
                }


            }
        }


        //-------------------------------------------------------------
        // ShowScrollUp
        //-------------------------------------------------------------
        private const string nameShowScrollUp = "ShowScrollUp";
        public static readonly BindableProperty ShowScrollUpProperty = BindableProperty.Create(nameShowScrollUp, typeof(bool), typeof(PageEnhancedNav), true); //, BindingMode.TwoWay
        public bool ShowScrollUp
        {
            get { return (bool)GetValue(ShowScrollUpProperty); }
            set { SetValue(ShowScrollUpProperty, value); }
        }




        public bool NoContentToDraw
        {
            get { return cContent.Width < 0 && cFullScreen.Width < 0; }
        }



        private AntiSpamClick CNavBar_OnDownIconScrollUp_Clicker;
        private async void CNavBar_OnDownIconScrollUp(object sender, EventArgs e)

        {
            if (CNavBar_OnDownIconScrollUp_Clicker == null)
            {
                CNavBar_OnDownIconScrollUp_Clicker = new AntiSpamClick(sender, e);
                CNavBar_OnDownIconScrollUp_Clicker.ClickFunc += async (sEnder, pArams) =>
                {


                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Update the UI
                        await ScrollToTop(true);
                    });


                };
            }
            CNavBar_OnDownIconScrollUp_Clicker.Click(sender, e);
        }


        public async Task ScrollToTop(bool animate)

        {
            try
            {
                await MainScroll.ScrollToAsync(0, 0, animate);
            }
            catch (Exception e)
            {
            }
        }


        // Left 1

        private AntiSpamClick CNavBar_OnDownLeftIcon1_Clicker;
        private async void CNavBar_OnDownLeftIcon1(object sender, EventArgs e) //tapped or dow event
        {
            if (CNavBar_OnDownLeftIcon1_Clicker == null)
                CNavBar_OnDownLeftIcon1_Clicker = new AntiSpamClick(CNavBar_OnDownLeftIcon1_Func);
            CNavBar_OnDownLeftIcon1_Clicker.Click(sender, e);
        }

        private async void CNavBar_OnDownLeftIcon1_Func(object sender, EventArgs e)

        {
            OnLeftIcon1Clicked();
        }

        // Left 2

        private AntiSpamClick CNavBar_OnDownLeftIcon2_Clicker;
        private async void CNavBar_OnDownLeftIcon2(object sender, EventArgs e) //tapped or dow event
        {
            if (CNavBar_OnDownLeftIcon2_Clicker == null)
                CNavBar_OnDownLeftIcon2_Clicker = new AntiSpamClick(CNavBar_OnDownLeftIcon2_Func);
            CNavBar_OnDownLeftIcon2_Clicker.Click(sender, e);
        }

        private async void CNavBar_OnDownLeftIcon2_Func(object sender, EventArgs e)

        {
            GetContent()?.OnRightIcon2Clicked();
            OnLeftIcon2Clicked();
        }

        // Right 1

        private AntiSpamClick CNavBar_OnDownRightIcon1_Clicker;
        private async void CNavBar_OnDownRightIcon1(object sender, EventArgs e) //tapped or dow event
        {
            if (CNavBar_OnDownRightIcon1_Clicker == null)
                CNavBar_OnDownRightIcon1_Clicker = new AntiSpamClick(CNavBar_OnDownRightIcon1_Func);
            CNavBar_OnDownRightIcon1_Clicker.Click(sender, e);
        }

        private async void CNavBar_OnDownRightIcon1_Func(object sender, EventArgs e)

        {
            GetContent()?.OnRightIcon1Clicked();
            OnRightIcon1Clicked();
        }

        // Right 2

        private AntiSpamClick CNavBar_OnDownRightIcon2_Clicker;
        private async void CNavBar_OnDownRightIcon2(object sender, EventArgs e) //tapped or dow event
        {
            if (CNavBar_OnDownRightIcon2_Clicker == null)
                CNavBar_OnDownRightIcon2_Clicker = new AntiSpamClick(CNavBar_OnDownRightIcon2_Func);
            CNavBar_OnDownRightIcon2_Clicker.Click(sender, e);
        }

        private async void CNavBar_OnDownRightIcon2_Func(object sender, EventArgs e)

        {
            GetContent()?.OnRightIcon2Clicked();
            OnRightIcon2Clicked();
        }


        //-------------------------------------------------------------
        // ShowGoBack
        //-------------------------------------------------------------
        private const string nameShowGoBack = "ShowGoBack";
        public static readonly BindableProperty ShowGoBackProperty = BindableProperty.Create(nameShowGoBack, typeof(bool), typeof(PageEnhancedNav), false); //, BindingMode.TwoWay
        public bool ShowGoBack
        {
            get { return (bool)GetValue(ShowGoBackProperty); }
            set { SetValue(ShowGoBackProperty, value); }
        }



        public virtual async void OnLeftIcon1Clicked()

        {
            if (ShowGoBack)
            {
                if (AlwaysShowMenu)
                {
                    ShowMenu();
                }
                else
                {
                    await OnBeforeGoBack(false);
                    if (CanNotGoBack) return;
                    Popping?.Invoke(this, new NavigationEventArgs(this));
                    await NavigateBack(); //ReleaseOnClosing called inside there
                }
            }
            else
                ShowMenu();


            //if ((Navigation.NavigationStack.Count > 1 || IsModal) && !PseudoTab)
            //{
            //    await OnBeforeGoBack(false);
            //    if (CanNotGoBack) return;
            //    Popping?.Invoke(this, new NavigationEventArgs(this));
            //    await NavigateBack(); //ReleaseOnClosing called inside there
            //}
            //else
            //    ShowMenu();


        }

        public void AnimateLogoImage()
        {
            cNavBar.AnimateLogoImage();
        }


        public virtual async void OnLeftIcon2Clicked()

        {
            if (AlwaysShowMenu)
            {
                await OnBeforeGoBack(false);
                if (CanNotGoBack) return;
                Popping?.Invoke(this, new NavigationEventArgs(this));
                await NavigateBack(); //ReleaseOnClosing called inside there
            }
            else
            {
                if (Navigation.NavigationStack.Count > 1)
                    ShowMenu();
            }
        }

        public virtual void OnRightIcon1Clicked() { }
        public virtual void OnRightIcon2Clicked() { }


        private void On_btnConnect(object sender, EventArgs e)

        {
            //ShowContent = false;
            PageOffline = false;
            Core.Current.Reconnect();
        }


        private INotifyPropertyChanged _oldBindingContext;

        protected override void OnBindingContextChanged()

        {
            if (_oldBindingContext != null)
            {
                _oldBindingContext.PropertyChanged -= OnBindingContextPropertyChanged;
            }


            if (BindingContext != null)
            {
                INotifyPropertyChanged ctx = null;
                try
                {
                    _oldBindingContext = (INotifyPropertyChanged)BindingContext;
                    ctx = (INotifyPropertyChanged)BindingContext;
                }
                catch
                {
                    Debug.WriteLine("PAGE ENHANCED NAV: BindingContext is not INotifyPropertyChanged");
                    base.OnBindingContextChanged();
                    return;
                }
                ctx.PropertyChanged += OnBindingContextPropertyChanged;

                //todo read default values just in case

                //todo and respond to them
                bool bValue;
                PropertyInfo propertyInfo;


                propertyInfo = BindingContext.GetType().GetProperty("IsLoading");
                if (propertyInfo != null)
                {
                    bValue = (bool)propertyInfo.GetValue(BindingContext);
                    //   if (!ManualLoader)
                    //     ShowContent = !bValue;
                }

                propertyInfo = BindingContext.GetType().GetProperty("IsOffline");
                if (propertyInfo != null)
                {
                    bValue = (bool)propertyInfo.GetValue(BindingContext);
                    PageOffline = bValue;
                }
            }

            base.OnBindingContextChanged();
        }

        public bool ManualLoader { get; set; }


        protected void ShowLoader(bool value)

        {
            if (value)
            {
                cContent.IsVisible = false;
                cFullScreen.IsVisible = false;
                cLoading.IsVisible = true;
            }
            else //loaded
            {
                cLoading.IsVisible = false;
                if (IsFullScreen)
                    cFullScreen.IsVisible = true;
                else
                    cContent.IsVisible = true;
            }
        }


        protected void ShowTransparentLoader(bool value)
        {
            if (value)
            {
                cLoading.IsVisible = true;
            }
            else //loaded
            {
                cLoading.IsVisible = false;
            }
        }


        private void OnBindingContextPropertyChanged(object sender, PropertyChangedEventArgs e)

        {
            var name = e.PropertyName;
            if (name == null || BindingContext == null) return;

            OnThisBindingContextPropertyChanged(sender, name);

        }


        public virtual void OnThisBindingContextPropertyChanged(object sender, string propertyName)

        {
            var propertyInfo = BindingContext.GetType().GetProperty(propertyName);

            if (propertyInfo == null) return;

            if (propertyName == "IsLoading")
            {
                bool propertyValue = (bool)propertyInfo.GetValue(BindingContext);
                if (propertyValue) //loading..
                {
                    if (OnceLoaded) return;
                }
                else //loaded
                {
                    OnceLoaded = true;
                }
                //   if (!ManualLoader)
                //   ShowContent = !propertyValue;
            }
            else
            if (propertyName == "IsOffline")
            {
                bool propertyValue = (bool)propertyInfo.GetValue(BindingContext);
                if (propertyValue) OnceLoaded = false;
                PageOffline = propertyValue;
            }

        }

        public virtual void ContentLoaderCheck(bool stop)
        {
            bLoaderCheck = stop;
        }
        private bool bLoaderCheck { get; set; }

        //
        //public async Task LaunchContentLoader()
        //
        //{
        ////    ShowContent = false;
        //    ManualLoader = true;

        //    Device.StartTimer(TimeSpan.FromSeconds(0.2), () =>
        //    {
        //        var ret = true;
        //        var wait = true;
        //        MainThread.BeginInvokeOnMainThread(() =>
        //        {
        //            // Update the UI
        //            ContentLoaderCheck(bLoaderCheck);
        //            if (bLoaderCheck)
        //            {
        //                //ShowContent = true;
        //                ManualLoader = false;
        //                ret= false;
        //            }
        //            else
        //            {
        //                ret= true;
        //            }
        //            wait = false;
        //        });
        //        while (wait)
        //        {
        //            Task.Delay(300);
        //        }
        //        return ret;
        //    });
        //    await Task.Delay(10);
        //}


        public void StopTransparentContentLoader()
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                cLoading.IsVisible = false;
            });
        }


        protected bool LoaderBusy { get; set; }
        private bool closeMe = false;
        private bool uiBusy = false;
        /// <summary>
        /// Close with StopTransparentContentLoader();
        /// </summary>
        /// <returns></returns>

        public async Task LaunchTransparentContentLoader()

        {
            if (LoaderBusy) return;
            LoaderBusy = true;

            //            ShowContent = true;
            //ManualLoader = true;

            cLoading.IsVisible = true;
            await Task.Delay(1);
            closeMe = false;
            Device.StartTimer(TimeSpan.FromSeconds(0.2), () =>
            {
                if (uiBusy) return true;
                if (closeMe)
                {
                    return false; //end
                }
                uiBusy = true;
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    // Update the UI
                    ContentLoaderCheck(bLoaderCheck);
                    if (bLoaderCheck || !cLoading.IsVisible)
                    {
                        cLoading.IsVisible = false;
                        LoaderBusy = false;
                        closeMe = true;
                        uiBusy = false;
                    }
                });
                return false; //repeat
            });
            await Task.Delay(1);
        }

        public static Task BeginInvokeOnMainThreadAsync(Action a)

        {
            var tcs = new TaskCompletionSource<bool>();
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    a();
                    tcs.SetResult(true);
                }
                catch (Exception ex)
                {
                    tcs.SetException(ex);
                }
            }); return tcs.Task;
        }

        //-----------------------------------------------------------------
        private void InitContents()
        //-----------------------------------------------------------------
        {
            if (IsFullScreen)
            {
                cFullScreenStack.IsVisible = true;
                MainScroll.IsVisible = false;
                cContent.IsVisible = false;
                cFullScreen.IsVisible = true;
            }
            else
            {
                cFullScreenStack.IsVisible = false;
                MainScroll.IsVisible = true;
                cContent.IsVisible = true;
                cFullScreen.IsVisible = false;
            }
        }

        //-------------------------------------------------------------
        // UseNavGradient
        //-------------------------------------------------------------
        private const string nameUseStatusGradient = "UseNavGradient";
        public static readonly BindableProperty UseStatusGradientProperty = BindableProperty.Create(nameUseStatusGradient, typeof(bool), typeof(PageEnhancedNav), false); //, BindingMode.TwoWay
        public bool UseNavGradient
        {
            get { return (bool)GetValue(UseStatusGradientProperty); }
            set { SetValue(UseStatusGradientProperty, value); }
        }


        //-------------------------------------------------------------
        // ShowLoaderOverlay
        //-------------------------------------------------------------
        private const string nameShowLoaderOverlay = "ShowLoaderOverlay";
        public static readonly BindableProperty ShowLoaderOverlayProperty = BindableProperty.Create(nameShowLoaderOverlay, typeof(bool), typeof(PageEnhancedNav), false); //, BindingMode.TwoWay
        public bool ShowLoaderOverlay
        {
            get { return (bool)GetValue(ShowLoaderOverlayProperty); }
            set { SetValue(ShowLoaderOverlayProperty, value); }
        }





        // DO NOT CLOSE - OnPropertyChanged

        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)

        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {
            //property changed
            case nameNavigationVisible:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                gridNavi.IsVisible = NavigationVisible;
                cNavBarPadding.IsVisible = NavigationVisible;
                cNavBarPadding2.IsVisible = NavigationVisible;
                cOverlayNavBar.IsVisible = !NavigationVisible;

            });

            //if (!NavigationVisible)
            //{
            //    rowStatusBar.Height = 0;
            //}
            break;

            case nameUseStatusGradient:
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                cNavStatusBar.UseGradient = UseNavGradient;

            });

            break;

            //property changed
            case nameShowScrollUp:
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                UpdateNavScroll(MainScroll.ScrollY);
            });
            break;

            //CustomPatternId
            case nameCustomPatternId:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                var generatedFilename = BaseMobileDto.GetThumbnailUrl(CustomPatternId, "normal", TenantOptions.TenantKey);
                srcNavigationBack.Source = generatedFilename;

            });

            break;

            //property changed
            case nameIsNavigationBackgroundVisible:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                imgNavigationBack.IsVisible = IsNavigationBackgroundVisible;

            });

            break;

            //property changed
            case nameNavigationBackgroundOpacity:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                imgNavigationBack.Opacity = NavigationBackgroundOpacity;

            });

            break;

            case nameSearchPlaceholder:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                cNavBar.SearchPlaceholder = SearchPlaceholder;

            });

            break;

            case nameMaxWidth:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                UpdateControls();

            });

            break;

            ////property changed
            case nameInsertPopup:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                cPopup.Content = InsertPopup;

            });

            break;

            case nameInsertContent:
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                if (true) //ShowContent
                {
                    if (IsFullScreen)
                        cFullScreen.Content = InsertContent as View;
                    else
                        cContent.Content = InsertContent as View;
                }
            });
            break;


            case nameShowLoaderOverlay:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                cLoading.IsVisible = ShowLoaderOverlay;

            });

            break;

            //property changed
            case nameIsFullScreen:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                InitContents();

            });

            break;

            case namePageOffline:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                if (PageOffline)
                {
                    MainScroll.IsVisible = false;
                    cOffline.IsVisible = true;
                }
                else
                {
                    cOffline.IsVisible = false;
                    MainScroll.IsVisible = true;
                }


            });
            break;

            //icons
            case nameRightIcon1Source:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                cNavBar.RightIcon1Source = RightIcon1Source;

            });

            break;
            case nameRightIcon2Source:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                cNavBar.RightIcon2Source = RightIcon2Source;

            });

            break;
            case nameLeftIcon1Source:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                cNavBar.LeftIcon1Source = LeftIcon1Source;

            });

            break;
            case nameLeftIcon2Source:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                cNavBar.LeftIcon2Source = LeftIcon2Source;

            });

            break;

            case "Title":
            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                cNavBar.Title = Title;
            });
            break;

            case "TitleFontFamily":

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                cNavBar.FontFamily = TitleFontFamily;

            });

            break;

            //case "TitleFontSize":
            //    cNavBar.FontSize = AppUI.NavBarFontTitleSize;
            //    break;

            case "TitleImage":

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI

                cNavBar.TitleImage = TitleImage;

            });
            break;

            //property changed
            case nameWallpaper:

            MainThread.BeginInvokeOnMainThread(() =>
            {
                // Update the UI
                if (Wallpaper == null)
                {
                    cWallpaperImage.Source = WallpaperBackup;
                }
                else
                {
                    cWallpaperImage.Source = Wallpaper;
                }
            });
            break;

            }

        }


        public async Task BuildContent(Type type)

        {
            if (InsertContent == null)
            {
                var create = (IIncludedContent)Activator.CreateInstance(type, this);
                //var create = new ContentAboutUs(this);
                InsertContent = create;
                if (create is View view)
                {
                    view.Opacity = 0;
                    await view.FadeTo(1, 200);
                }
            }
        }

        public bool CreateContent(Type type)

        {
            if (InsertContent == null)
            {
                var create = (IIncludedContent)Activator.CreateInstance(type, this);
                InsertContent = create;
            }
            return true;
        }


        //private void _ReplaceContent(Type type, params object[] args)
        //{
        //    if (InsertContent != null)
        //    {
        //        //try dispose
        //        InsertContent.Dispose();
        //    }

        //    if (args.Length != 0)
        //    {
        //        var create = (IIncludedContent)Activator.CreateInstance(type, this, args[0]);
        //        InsertContent = create;
        //    }
        //    else
        //    {
        //        var create = (IIncludedContent)Activator.CreateInstance(type, this);
        //        InsertContent = create;
        //    }
        //}


        public bool ReplaceContent(Type type, params object[] args)

        {
            //if (InsertContent != null)
            //{
            //    //try dispose
            //    InsertContent.Dispose();
            //}

            if (args.Length != 0)
            {
                var create = (IIncludedContent)Activator.CreateInstance(type, this, args[0]);
                create.Init(this);
                InsertContent = create;
            }
            else
            {
                var create = (IIncludedContent)Activator.CreateInstance(type, this);
                create.Init(this);
                InsertContent = create;
            }

            return true;

        }


        public bool ReplacePopup(Type type)

        {
            var create = (IncludedPopup)Activator.CreateInstance(type, this);
            InsertPopup = create;
            return true;
        }



        //-------------------------------------------------------------
        // AlwaysShowMenu
        //-------------------------------------------------------------
        private const string nameAlwaysShowMenu = "AlwaysShowMenu";
        public static readonly BindableProperty AlwaysShowMenuProperty = BindableProperty.Create(nameAlwaysShowMenu, typeof(bool), typeof(PageEnhancedNav), false); //, BindingMode.TwoWay
        public bool AlwaysShowMenu
        {
            get { return (bool)GetValue(AlwaysShowMenuProperty); }
            set { SetValue(AlwaysShowMenuProperty, value); }
        }

        //-------------------------------------------------------------
        // PseudoTab
        //-------------------------------------------------------------
        //private const string namePseudoTab = "PseudoTab";
        //public static readonly BindableProperty PseudoTabProperty = BindableProperty.Create(namePseudoTab, typeof(bool), typeof(PageEnhancedNav), false); //, BindingMode.TwoWay
        //public bool PseudoTab
        //{
        //    get { return (bool)GetValue(PseudoTabProperty); }
        //    set { SetValue(PseudoTabProperty, value); }
        //}	


        //-------------------------------------------------------------
        // InsertPopup
        //-------------------------------------------------------------
        private const string nameInsertPopup = "InsertPopup";
        public static readonly BindableProperty InsertPopupProperty = BindableProperty.Create(nameInsertPopup, typeof(IncludedPopup), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public IncludedPopup InsertPopup
        {
            get { return (IncludedPopup)GetValue(InsertPopupProperty); }
            set { SetValue(InsertPopupProperty, value); }
        }



        public void ToggleButtonVisibility(ButtonType button, bool visible)

        {
            // Debug.WriteLine($"[TOGGLED] {button} visible {visible}");
            cNavBar.ToggleButtonVisibility(button, visible);
        }

        public async Task ToggleSearch()

        {
            await cNavBar.ToggleSearch();
        }


        private double lastScrollY { get; set; } = 0;
        private ScrollDirection lastScrollDirection { get; set; } = ScrollDirection.None;
        private double startingY { get; set; } = 0;

        //-----------------------------------------------------------------
        protected void ProcessScrollingState()
        //-----------------------------------------------------------------
        {
            var pos = MainScroll.ScrollY;

            if (pos >= 0)
            {
                var direction = ScrollDirection.None;
                if (pos > lastScrollY)
                {
                    direction = ScrollDirection.Down;
                }
                else
                if (pos < lastScrollY)
                {
                    direction = ScrollDirection.Up;
                }
                lastScrollY = pos;


                var k = 1;
                //overflow check
                //if (pos > MainScroll.Height)
                //   pos = MainScroll.Height;

                //  Debug.WriteLine("*Scrolled "+pos);

                //koeff scrolled amount from 0 to 1
                var scrolledAmount = Math.Abs(1 - MainScroll.Height / (MainScroll.Height - pos));

                //calculate translation
                var translation = -cNavBar.Height * scrolledAmount;

                var opacity = 1.0;

                //do not translate show more than max height
                if (translation <= 0)
                {
                    cNavBar.TranslationY = translation;
                    boxNavBar.TranslationY = translation;
                    opacity = 2 - Math.Abs((1 - translation / cNavBar.Height));
                }
                else
                {
                    cNavBar.TranslationY = 0;
                    boxNavBar.TranslationY = 0;
                }

                //do not translate hide more than max height
                if (translation >= -cNavBar.Height)
                    imgNavigationBack.TranslationY = translation;
                else
                    imgNavigationBack.TranslationY = -cNavBar.Height;

                //boxNavBar.Opacity = opacity;
                //cNavBar.Opacity = opacity;

                gridNavi.Opacity = opacity;

                //  Title = opacity.ToString();

                //show scroll up button if needed
                if (pos > 10)
                {
                    if (ShowScrollUp)
                        cOverlayScrollUp.IsVisible = true;
                    else
                        cOverlayScrollUp.IsVisible = false;
                }
                else
                    cOverlayScrollUp.IsVisible = false;

                //show collapsed button if bar hidden
                if (!IsFullScreen)
                {
                    if (pos > cNavBar.Height * 3)
                    {
                        cOverlayNavBar.IsVisible = true;
                    }
                    else
                        cOverlayNavBar.IsVisible = false;
                }
                //if (Math.Abs(translation) > cNavBar.Height)
                //    cOverlayNavBar.IsVisible = true;
                //else
                //    cOverlayNavBar.IsVisible = false;

            }



        }


        private void OnScrolled_ScrollView(object sender, ScrolledEventArgs e)

        {
            // OnScrolling for NAVIGATION with *** STANDART *** image
            ProcessScrollingState();

        }

        //tuch highlighting below
        public bool IsPressed { get; set; } = false;
        public bool IsPanned { get; set; } = false;

        private Color OriginalBackColor = Colors.Transparent;

        private AppoMobi.Touch.LegacyGesturesGrid CurrentSelectionGrid = null;
        private AppoMobi.Touch.LegacyGesturesStackLayout CurrentSelectionStack = null;

        private object SelectedIcon = null;



        bool _growbusy = false;
        protected async Task Grow(CachedImage myobject)

        {
            if (_growbusy) return;

            _growbusy = true;
            try
            {
                while (IsPressed)
                {
                    myobject.Transformations.Clear();
                    myobject.Transformations.Add(new TintTransformation
                    { HexColor = AppColors.primary_dark, EnableSolidColor = true });
                    myobject.ReloadImage();
                    await myobject.ScaleTo(1.25, 75);
                    await myobject.ScaleTo(1.0, 75);
                    myobject.Transformations.Clear();
                    myobject.Transformations.Add(new TintTransformation
                    {
                        HexColor = AppColors.primary_darkest,
                        EnableSolidColor = true
                    });
                    myobject.ReloadImage();
                    await myobject.ScaleTo(1.20, 75);
                    await myobject.ScaleTo(1.0, 75);
                }
            }
            catch
            {

            }
            myobject.Transformations.Clear();
            myobject.ReloadImage();
            _growbusy = false;
        }


        //-------------------------------------------------------------
        // SelectionColor
        //-------------------------------------------------------------
        private const string nameSelectionColor = "SelectionColor";
        public static readonly BindableProperty SelectionColorProperty = BindableProperty.Create(nameSelectionColor, typeof(Color), typeof(PageEnhancedNav), AppColors.PrimaryHighlight); //, BindingMode.TwoWay
        public Color SelectionColor
        {
            get { return (Color)GetValue(SelectionColorProperty); }
            set { SetValue(SelectionColorProperty, value); }
        }


        public void OnCellDown(object sender, DownUpEventArgs e)

        {
            if (sender == null) return;

            IsPressed = true;
            Debug.WriteLine("*** DOWN");

            var cc = new Color();
            cc = SelectionColor;

            if (sender is AppoMobi.Touch.LegacyGesturesGrid)
            {
                var sel1 = (AppoMobi.Touch.LegacyGesturesGrid)sender;
                if (CurrentSelectionGrid != null)
                {
                    CurrentSelectionGrid.BackgroundColor = OriginalBackColor;
                }
                CurrentSelectionGrid = sel1;
                sel1.BackgroundColor = cc;
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    // Update the UI
                    //Grow(ss.SelectedIcon); //try catch inside
                });
            }
            else
            if (sender is AppoMobi.Touch.LegacyGesturesStackLayout)
            {
                var sel2 = (AppoMobi.Touch.LegacyGesturesStackLayout)sender;
                if (CurrentSelectionStack != null)
                {
                    CurrentSelectionStack.BackgroundColor = OriginalBackColor;
                }
                CurrentSelectionStack = sel2;
                sel2.BackgroundColor = cc;
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    // Update the UI
                    //Grow(ss.SelectedIcon); //try catch inside
                });
            }

            Debug.WriteLine("*** DOWN");
        }

        public void OnCellUp(object sender, DownUpEventArgs e)

        {
            if (sender == null) return;

            IsPressed = false;
            if (sender is AppoMobi.Touch.LegacyGesturesGrid)
                CurrentSelectionGrid.BackgroundColor = OriginalBackColor;
            else
            if (sender is AppoMobi.Touch.LegacyGesturesStackLayout)
                CurrentSelectionStack.BackgroundColor = OriginalBackColor;

            //if (CurrentSelectionBox != null && DeviceInfo.Current.Platform != DevicePlatform.Android)
            //{
            //    CurrentSelectionBox.BackgroundColor = OriginalBackColor;
            //}
            Debug.WriteLine("*** UP");
        }

        public void OnCellPanned(object sender, PanEventArgs e)

        {
            if (sender == null) return;
            Debug.WriteLine("*** PANNED");
        }

        #region Properties


        //-------------------------------------------------------------
        // NavigationBackgroundOpacity
        //-------------------------------------------------------------
        private const string nameNavigationBackgroundOpacity = "NavigationBackgroundOpacity";
        public static readonly BindableProperty NavigationBackgroundOpacityProperty = BindableProperty.Create(nameNavigationBackgroundOpacity, typeof(double), typeof(PageEnhancedNav), 0.9); //, BindingMode.TwoWay
        public double NavigationBackgroundOpacity
        {
            get { return (double)GetValue(NavigationBackgroundOpacityProperty); }
            set { SetValue(NavigationBackgroundOpacityProperty, value); }
        }


        //-------------------------------------------------------------
        // IsNavigationBackgroundVisible
        //-------------------------------------------------------------
        private const string nameIsNavigationBackgroundVisible = "IsNavigationBackgroundVisible";
        public static readonly BindableProperty IsNavigationBackgroundVisibleProperty = BindableProperty.Create(nameIsNavigationBackgroundVisible, typeof(bool), typeof(PageEnhancedNav), true); //, BindingMode.TwoWay
        public bool IsNavigationBackgroundVisible
        {
            get { return (bool)GetValue(IsNavigationBackgroundVisibleProperty); }
            set { SetValue(IsNavigationBackgroundVisibleProperty, value); }
        }


        //-------------------------------------------------------------
        // IsFullScreen
        //-------------------------------------------------------------
        private const string nameIsFullScreen = "IsFullScreen";
        public static readonly BindableProperty IsFullScreenProperty = BindableProperty.Create(nameIsFullScreen, typeof(bool), typeof(PageEnhancedNav), false); //, BindingMode.TwoWay
        public bool IsFullScreen
        {
            get { return (bool)GetValue(IsFullScreenProperty); }
            set { SetValue(IsFullScreenProperty, value); }
        }

        //-------------------------------------------------------------
        // NavigationVisible
        //-------------------------------------------------------------
        private const string nameNavigationVisible = "NavigationVisible";
        public static readonly BindableProperty NavigationVisibleProperty = BindableProperty.Create(nameNavigationVisible, typeof(bool), typeof(PageEnhancedNav), true); //, BindingMode.TwoWay
        public bool NavigationVisible
        {
            get { return (bool)GetValue(NavigationVisibleProperty); }
            set { SetValue(NavigationVisibleProperty, value); }
        }



        // MaxWidth

        private const string nameMaxWidth = "MaxWidth";
        public static readonly BindableProperty MaxWidthProperty = BindableProperty.Create(nameMaxWidth, typeof(double), typeof(PageEnhancedNav), 550.0); //, BindingMode.TwoWay
        public double MaxWidth
        {
            get { return (double)GetValue(MaxWidthProperty); }
            set { SetValue(MaxWidthProperty, value); }
        }


        // Wallpaper

        private const string nameWallpaper = "Wallpaper";
        public static readonly BindableProperty WallpaperProperty = BindableProperty.Create(nameWallpaper, typeof(ImageSource), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public ImageSource Wallpaper
        {
            get { return (ImageSource)GetValue(WallpaperProperty); }
            set { SetValue(WallpaperProperty, value); }
        }




        // DisplayLoadingOnce

        private const string nameDisplayLoadingOnce = "DisplayLoadingOnce";
        public static readonly BindableProperty DisplayLoadingOnceProperty = BindableProperty.Create(nameDisplayLoadingOnce, typeof(bool), typeof(PageEnhancedNav), true); //, BindingMode.TwoWay
        /// <summary>
        /// if page loaded once then when loading second and more times no need to 
        /// show the loading part
        /// </summary>
        public bool DisplayLoadingOnce
        {
            get { return (bool)GetValue(DisplayLoadingOnceProperty); }
            set { SetValue(DisplayLoadingOnceProperty, value); }
        }
        private bool OnceLoaded { get; set; }


        // PageOffline

        private const string namePageOffline = "PageOffline";
        public static readonly BindableProperty PageOfflineProperty = BindableProperty.Create(namePageOffline, typeof(bool), typeof(PageEnhancedNav), false); //, BindingMode.TwoWay
        public bool PageOffline
        {
            get { return (bool)GetValue(PageOfflineProperty); }
            set { SetValue(PageOfflineProperty, value); }
        }



        //
        //// ShowContent
        //
        //private const string nameShowContent = "ShowContent";
        //public static readonly BindableProperty ShowContentProperty = BindableProperty.Create(nameShowContent, typeof(bool), typeof(PageEnhancedNav), true); //, BindingMode.TwoWay
        ///// <summary>
        ///// if set to FALSE content will be shown manually after load
        ///// </summary>
        //public bool ShowContent
        //{
        //    get { return (bool)GetValue(ShowContentProperty); }
        //    set { SetValue(ShowContentProperty, value); }
        //}	


        // InsertContent

        private const string nameInsertContent = "InsertContent";
        public static readonly BindableProperty InsertContentProperty = BindableProperty.Create(nameInsertContent, typeof(IIncludedContent), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public IIncludedContent InsertContent
        {
            get { return (IIncludedContent)GetValue(InsertContentProperty); }
            set
            {
                IIncludedContent tmp = null;
                if (value != InsertContent)
                    tmp = InsertContent;
                SetValue(InsertContentProperty, value);
                tmp?.Dispose();
            }
        }


        // ContentPlaceholder

        private const string nameContentPlaceholder = "ContentPlaceholder";
        public static readonly BindableProperty ContentPlaceholderProperty = BindableProperty.Create(nameContentPlaceholder, typeof(IIncludedContent), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public IIncludedContent ContentPlaceholder
        {
            get { return (IIncludedContent)GetValue(ContentPlaceholderProperty); }
            set { SetValue(ContentPlaceholderProperty, value); }
        }


        // SearchPlaceholder

        private const string nameSearchPlaceholder = "SearchPlaceholder";
        public static readonly BindableProperty SearchPlaceholderProperty = BindableProperty.Create(nameSearchPlaceholder, typeof(string), typeof(PageEnhancedNav), ResStrings.EnterString); //, BindingMode.TwoWay
        public string SearchPlaceholder
        {
            get { return (string)GetValue(SearchPlaceholderProperty); }
            set { SetValue(SearchPlaceholderProperty, value); }
        }


        // TitleImage

        private const string nameTitleImage = "TitleImage";
        public static readonly BindableProperty TitleImageProperty = BindableProperty.Create(nameTitleImage, typeof(string), typeof(PageEnhancedNav), ResStrings.EnterString); //, BindingMode.TwoWay
        public string TitleImage
        {
            get { return (string)GetValue(TitleImageProperty); }
            set { SetValue(TitleImageProperty, value); }
        }


        // LeftIcon2Source

        private const string nameLeftIcon2Source = "LeftIcon2Source";
        public static readonly BindableProperty LeftIcon2SourceProperty = BindableProperty.Create(nameLeftIcon2Source, typeof(ImageSource), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public ImageSource LeftIcon2Source
        {
            get { return (ImageSource)GetValue(LeftIcon2SourceProperty); }
            set { SetValue(LeftIcon2SourceProperty, value); }
        }

        //-------------------------------------------------------------
        // LeftIcon2Tag
        //-------------------------------------------------------------
        private const string nameLeftIcon2Tag = "LeftIcon2Tag";
        public static readonly BindableProperty LeftIcon2TagProperty = BindableProperty.Create(nameLeftIcon2Tag, typeof(string), typeof(PageEnhancedNav), string.Empty); //, BindingMode.TwoWay
        public string LeftIcon2Tag
        {
            get { return (string)GetValue(LeftIcon2TagProperty); }
            set { SetValue(LeftIcon2TagProperty, value); }
        }

        //-------------------------------------------------------------
        // LeftIcon1Tag
        //-------------------------------------------------------------
        private const string nameLeftIcon1Tag = "LeftIcon1Tag";
        public static readonly BindableProperty LeftIcon1TagProperty = BindableProperty.Create(nameLeftIcon1Tag, typeof(string), typeof(PageEnhancedNav), string.Empty); //, BindingMode.TwoWay
        public string LeftIcon1Tag
        {
            get { return (string)GetValue(LeftIcon1TagProperty); }
            set { SetValue(LeftIcon1TagProperty, value); }
        }

        //-------------------------------------------------------------
        // RightIcon2Tag
        //-------------------------------------------------------------
        private const string nameRightIcon2Tag = "RightIcon2Tag";
        public static readonly BindableProperty RightIcon2TagProperty = BindableProperty.Create(nameRightIcon2Tag, typeof(string), typeof(PageEnhancedNav), string.Empty); //, BindingMode.TwoWay
        public string RightIcon2Tag
        {
            get { return (string)GetValue(RightIcon2TagProperty); }
            set { SetValue(RightIcon2TagProperty, value); }
        }

        //-------------------------------------------------------------
        // RightIcon1Tag
        //-------------------------------------------------------------
        private const string nameRightIcon1Tag = "RightIcon1Tag";
        public static readonly BindableProperty RightIcon1TagProperty = BindableProperty.Create(nameRightIcon1Tag, typeof(string), typeof(PageEnhancedNav), string.Empty); //, BindingMode.TwoWay
        public string RightIcon1Tag
        {
            get { return (string)GetValue(RightIcon1TagProperty); }
            set { SetValue(RightIcon1TagProperty, value); }
        }




        // RightIcon2Source

        private const string nameRightIcon2Source = "RightIcon2Source";
        public static readonly BindableProperty RightIcon2SourceProperty = BindableProperty.Create(nameRightIcon2Source, typeof(ImageSource), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public ImageSource RightIcon2Source
        {
            get { return (ImageSource)GetValue(RightIcon2SourceProperty); }
            set { SetValue(RightIcon2SourceProperty, value); }
        }


        //-------------------------------------------------------------
        // RightIcon2Symbol
        //-------------------------------------------------------------
        private const string nameRightIcon2Symbol = "RightIcon2Symbol";
        public static readonly BindableProperty RightIcon2SymbolProperty = BindableProperty.Create(nameRightIcon2Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel RightIcon2Symbol
        {
            get { return cNavBar.RightIcon2Symbol; }
        }

        //-------------------------------------------------------------
        // RightIcon1Symbol
        //-------------------------------------------------------------
        private const string nameRightIcon1Symbol = "RightIcon1Symbol";
        public static readonly BindableProperty RightIcon1SymbolProperty = BindableProperty.Create(nameRightIcon1Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel RightIcon1Symbol
        {
            get { return cNavBar.RightIcon1Symbol; }
        }


        //-------------------------------------------------------------
        // LeftIcon2Symbol
        //-------------------------------------------------------------
        private const string nameLeftIcon2Symbol = "LeftIcon2Symbol";
        public static readonly BindableProperty LeftIcon2SymbolProperty = BindableProperty.Create(nameLeftIcon2Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel LeftIcon2Symbol
        {
            get { return cNavBar.LeftIcon2Symbol; }
        }

        //-------------------------------------------------------------
        // OverlayNavBar 
        //-------------------------------------------------------------
        private const string nameOverlayNavBar = "OverlayNavBar";
        public static readonly BindableProperty OverlayNavBarProperty = BindableProperty.Create(nameOverlayNavBar, typeof(OverlayNavBarIconed), typeof(NavBar), null); //, BindingMode.TwoWay
        public OverlayNavBarIconed OverlayNavBar
        {
            get { return cOverlayNavBar; }
        }

        //-------------------------------------------------------------
        // LeftIcon1Symbol
        //-------------------------------------------------------------
        private const string nameLeftIcon1Symbol = "LeftIcon1Symbol";
        public static readonly BindableProperty LeftIcon1SymbolProperty = BindableProperty.Create(nameLeftIcon1Symbol, typeof(FontIconLabel), typeof(NavBar), null); //, BindingMode.TwoWay
        public FontIconLabel LeftIcon1Symbol
        {
            get { return cNavBar.LeftIcon1Symbol; }
        }



        // RightIcon1Source

        private const string nameRightIcon1Source = "RightIcon1Source";
        public static readonly BindableProperty RightIcon1SourceProperty = BindableProperty.Create(nameRightIcon1Source, typeof(ImageSource), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public ImageSource RightIcon1Source
        {
            get { return (ImageSource)GetValue(RightIcon1SourceProperty); }
            set { SetValue(RightIcon1SourceProperty, value); }
        }


        // LeftIcon1Source

        private const string nameLeftIcon1Source = "LeftIcon1Source";
        public static readonly BindableProperty LeftIcon1SourceProperty = BindableProperty.Create(nameLeftIcon1Source, typeof(ImageSource), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public ImageSource LeftIcon1Source
        {
            get { return (ImageSource)GetValue(LeftIcon1SourceProperty); }
            set { SetValue(LeftIcon1SourceProperty, value); }
        }




        // TitleFontSize

        //private const string nameTitleFontSize = "TitleFontSize";

        //public static readonly BindableProperty TitleFontSizeProperty = BindableProperty.Create(nameTitleFontSize, typeof(double), typeof(PageEnhancedNav), 1.0d);
        //public double TitleFontSize
        //{
        //    get { return (double)GetValue(TitleFontSizeProperty); }
        //    set { SetValue(TitleFontSizeProperty, value); }
        //}

        // TitleFontFamily

        private const string nameTitleFontFamily = "TitleFontFamily";
        public static readonly BindableProperty TitleFontFamilyProperty = BindableProperty.Create(nameTitleFontFamily, typeof(string), typeof(PageEnhancedNav), string.Empty); //, BindingMode.TwoWay
        public string TitleFontFamily
        {
            get { return (string)GetValue(TitleFontFamilyProperty); }
            set { SetValue(TitleFontFamilyProperty, value); }
        }

        // TitleTextColor

        private const string nameTitleTextColor = "TitleTextColor";
        public static readonly BindableProperty TitleTextColorProperty = BindableProperty.Create(nameTitleTextColor, typeof(Color), typeof(PageEnhancedNav), Colors.White); //, BindingMode.TwoWay
        public Color TitleTextColor
        {
            get { return (Color)GetValue(TitleTextColorProperty); }
            set { SetValue(TitleTextColorProperty, value); }
        }

        // TitleFontAttributes

        private const string nameTitleFontAttributes = "TitleFontAttributes";
        public static readonly BindableProperty TitleFontAttributesProperty = BindableProperty.Create(nameTitleFontAttributes, typeof(FontAttributes), typeof(PageEnhancedNav), new FontAttributes()); //, BindingMode.TwoWay
        public FontAttributes TitleFontAttributes
        {
            get { return (FontAttributes)GetValue(TitleFontAttributesProperty); }
            set { SetValue(TitleFontAttributesProperty, value); }
        }

        //-------------------------------------------------------------
        // CustomPatternId
        //-------------------------------------------------------------
        private const string nameCustomPatternId = "CustomPatternIdId";
        public static readonly BindableProperty CustomPatternIdProperty = BindableProperty.Create(nameCustomPatternId, typeof(string), typeof(PageEnhancedNav), null); //, BindingMode.TwoWay
        public string CustomPatternId
        {
            get { return (string)GetValue(CustomPatternIdProperty); }
            set { SetValue(CustomPatternIdProperty, value); }
        }


        #endregion
    }
}
