﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PageCustomized
    x:Class="AppoMobi.Pages.FbNews"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    ios:Page.UseSafeArea="False"
    BackgroundColor="{x:Static appoMobi:AppColors.PrimaryLightest}"
    IsFullScreen="True"
    NavigationPage.BackButtonTitle="{x:Static resX:ResStrings.GoBack}"
    NavigationPage.HasNavigationBar="False">

    <pages:PageEnhancedNav.InsertContent>
        <pages:IncludedContent Margin="0" HorizontalOptions="FillAndExpand">

            <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">

                <xam:FacebookWidgetWebView
                    x:Name="ControlBrowser"
                    Clicked="OnClicked"
                    CustomNavigated="MyBrowser_OnNavigated"
                    CustomNavigating="MyBrowser_OnNavigating"
                    HorizontalOptions="FillAndExpand"
                    Navigated="MyBrowser_OnNavigated"
                    Navigating="MyBrowser_OnNavigating"
                    SizeChanged="ControlBrowser_OnSizeChanged"
                    VerticalOptions="FillAndExpand"
                    WebViewColor="Black" />


                <BoxView
                    x:Name="ControlHide"
                    BackgroundColor="{Binding Source={x:Reference ControlBrowser}, Path=WebViewColor}"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="FillAndExpand" />

            </Grid>





        </pages:IncludedContent>
    </pages:PageEnhancedNav.InsertContent>

</pages:PageCustomized>


