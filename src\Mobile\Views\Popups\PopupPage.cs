﻿using CommunityToolkit.Maui;
using CommunityToolkit.Maui.Core;
using CommunityToolkit.Maui.Views;
using DrawnUi.Extensions;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AppoMobi.Mobile.Views.Popups
{
    public class PopupPage : CommunityToolkit.Maui.Views.Popup
    {
        private static Stack<PopupPage> PopupStack = new();

        public static void CloseAllPopups()
        {
            while (PopupStack.Count>0)
            {
                if (PopupStack.TryPop(out var popup))
                {
                    popup.Dismiss();
                }
            }
        }

        public void Open(bool animate = true)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                PopupStack.Push(this);
                App.Current.MainPage.ShowPopup(this);
            });
        }

        protected override Task OnClosed(object? result, bool wasDismissedByTappingOutsideOfPopup,
            CancellationToken token = new CancellationToken())
        {

            Debug.WriteLine("Closing POPUP");

            App.Instance.Messager.Unsubscribe(this, "CloseAllPopups");
            App.Instance.Messager.Unsubscribe(this, "NeedRestart");

            var ret = base.OnClosed(result, wasDismissedByTappingOutsideOfPopup, token);

            if (Content is IDisposable disposable)
            {
                disposable.Dispose();
            }

            return ret;
        }




        public PopupPage()
        {
            App.Instance.Messager.Subscribe<string>(this, "NeedRestart", async (sender, arg) =>
            {
                Dismiss();
            });

            App.Instance.Messager.Subscribe<string>(this, "CloseAllPopups", async (sender, arg) =>
            {
                Dismiss();
            });
        }

        public virtual void Dismiss()
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                Close();
            });
        }

     
        //protected override bool OnBackButtonPressed()

        //{
        //    if (QuitOnBackPressed)
        //        Core.Native.CloseApp();

        //    //return true; 
        //    return base.OnBackButtonPressed();
        //    // Prevent hide popup

        //}


        private string _Title;
        public string Title
        {
            get
            {
                return _Title;
            }
            set
            {
                if (_Title != value)
                {
                    _Title = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// TODO
        /// </summary>
        public bool CloseWhenBackgroundIsClicked => CanBeDismissedByTappingOutsideOfPopup;

        public bool HasSystemPadding { get; set; }

        public object Animation { get; set; }

        protected virtual bool OnBackgroundClicked()
        {
            return CloseWhenBackgroundIsClicked;
        }

     



    }
}
