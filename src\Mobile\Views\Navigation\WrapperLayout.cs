﻿using Microsoft.Maui.Layouts;

namespace AppoMobi.Mobile;

public class WrapperLayout : Layout, ILayoutManager
{
    public const double Minimum = 0;
    public const double Unset = double.NaN;
    public const double Maximum = double.PositiveInfinity;
    private readonly double MaximumHeight = -1;
    private readonly double MaximumWidth = -1;
    private readonly double MinimumHeight = -1;
    private readonly double MinimumWidth = -1;
    public Size MeasuredSize { get; protected set; }

    public new Size Measure(double widthConstraint, double heightConstraint) //ILayoutManager
    {
        var _layout = this;

        var padding = _layout.Padding;

        widthConstraint -= padding.HorizontalThickness;
        heightConstraint -= padding.VerticalThickness;

        double totalWidth = 0;
        double totalHeight = 0;

        for (var n = 0; n < _layout.Count; n++)
        {
            var child = _layout[n];
            if (child.Visibility == Visibility.Collapsed) continue;

            var measure = child.Measure(widthConstraint, heightConstraint);

            totalWidth = Math.Max(totalWidth, measure.Width);
            totalHeight = Math.Max(totalHeight, measure.Height);
        }


        // Account for padding
        totalWidth += padding.HorizontalThickness;
        totalHeight += padding.VerticalThickness;

        // Ensure that the total size of the layout fits within its constraints
        var finalWidth = ResolveConstraints(widthConstraint, _layout.Width, totalWidth, _layout.MinimumWidth,
            _layout.MaximumWidth);
        var finalHeight = ResolveConstraints(heightConstraint, _layout.Height, totalHeight, _layout.MinimumHeight,
            _layout.MaximumHeight);

        return new Size(finalWidth, finalHeight);
    }

    public Size ArrangeChildren(Rect bounds) //ILayoutManager
    {
        Measure(0, 0);

        foreach (var child in Children)
        {
            if (child.Visibility == Visibility.Collapsed) continue;
            child.Arrange(bounds);
        }

        return bounds.Size;
    }

    protected override ILayoutManager CreateLayoutManager()
    {
        return this;
    }

    public static double ResolveConstraints(double externalConstraint, double explicitLength, double measuredLength,
        double min = Minimum, double max = Maximum)
    {
        var length = IsExplicitSet(explicitLength) ? explicitLength : measuredLength;

        if (max < length) length = max;

        if (min > length) length = min;

        return Math.Min(length, externalConstraint);
    }

    public static bool IsExplicitSet(double value)
    {
        return !double.IsNaN(value);
    }
}