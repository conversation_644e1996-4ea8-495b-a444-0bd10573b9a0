﻿using AppoMobi.Touch;
using AppoMobi.Mobile.Views.Popups;
using DrawnUi.Draw;
using DrawnUi.Extensions;
using System.Diagnostics;
using Color = Microsoft.Maui.Graphics.Color;
using Point = Microsoft.Maui.Graphics.Point;
using Size = Microsoft.Maui.Graphics.Size;

namespace AppoMobi.Xam
{
    public partial class MultiSelectionListViewDrawn : PopupPage
    {


        public void UpdateTitle()
        {
            if (MultiselectMax < 1)
            {
                Title = _title;
                cTitle.Text = Title;
                return;
            }
            if (MultiselectMin < 1)
                Title = string.Format(_title, CountSelected, MultiselectMax);
            else
                Title = string.Format(_title, CountSelected, MultiselectMax, MultiselectMin);
            cTitle.Text = Title;
        }

        public int CountSelected

        {
            get { return MenuList.Count(x => x.Selected); }
        }

        public bool CanSelect

        {
            get
            {
                if (MultiselectMax < 1) return true;
                if (CountSelected < MultiselectMax) return true;
                return false;
            }
        }

        public bool NeedSelectMore

        {
            get
            {
                if (MultiselectMin < 1) return false;
                if (CountSelected < MultiselectMin) return true;
                return false;
            }
        }

        private int _MultiselectMin;
        public int MultiselectMin
        {
            get { return _MultiselectMin; }
            set
            {
                if (_MultiselectMin != value)
                {
                    _MultiselectMin = value;
                    OnPropertyChanged(nameof(MultiselectMin));
                    UpdateTitle();
                }
            }
        }

        private int _MultiselectMax;
        public int MultiselectMax
        {
            get { return _MultiselectMax; }
            set
            {
                if (_MultiselectMax != value)
                {
                    _MultiselectMax = value;
                    OnPropertyChanged(nameof(MultiselectMax));
                    UpdateTitle();
                }
            }
        }

        public bool Multiselect { get; set; }

        public NiftyObservableCollection<SelectionListItem> MenuList { get; } =
            new NiftyObservableCollection<SelectionListItem>();

        public Dictionary<string, bool> ValuesList;

        public string _title;

        public Task<Dictionary<string, bool>> Present()
        {
            _taskResult = new TaskCompletionSource<Dictionary<string, bool>>();

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(500), () =>
            {
                Open();
            });

            return _taskResult.Task;
        }

        protected override async Task OnClosed(object? result, bool wasDismissedByTappingOutsideOfPopup,
            CancellationToken token = new CancellationToken())
        {
            if (_taskResult != null)
            {
                if (!_taskResult.Task.IsCompleted)
                    _taskResult.SetResult(this.ValuesList);
            }
        }


        public MultiSelectionListViewDrawn(Action<Dictionary<string, bool>> callback, string title,
            Dictionary<string, bool> list,
                string cancel,
                bool disableBackgroundclick = false,
                bool quitOnBackPressed = false)
        {
            InitializeComponent();


            _title = title;

            CallbackListKeys = callback;
            ValuesList = list;


            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                cTitle.TranslationY = 1;
            }

            //todo selected
            var ii = 0;
            foreach (var item in list)
            {
                var add = new SelectionListItem();
                add.Title = item.Key;
                add.Selected = item.Value;
                ii++;
                MenuList.Add(add);
            }

            //submit =)
            BindingContext = this;

            UpdateTitle();

            if (list != null && list.Any())
            {

            }
            else
                Grid_OnDown(null, null);

            //cDataStack.ItemsSource = MenuList;
        }


        private bool _tapped = false;
        //-------------------------------------------------------------
        private async void OnTapped_Item(object sender, TapEventArgs e)
        //-------------------------------------------------------------
        {
            if (_tapped) return;
            _tapped = true;

            _gridOnDownFired = false;

            Debug.WriteLine("[MULTISELECTION] Tapped");


            //var stack = (AppoMobi.Touch.StackLayout) sender;
            var item = (SelectionListItem)e.Sender.DownCommandParameter;

            //var item = (SelectionListItem)e.Sender.TappedCommandParameter;
            if (item == null) return;

            //todo process tap
            try
            {
                var oldValue = ValuesList[item.Title];
                if (!Multiselect)
                {
                    if (oldValue)
                    {
                        //already selected
                        await DismissDialog(ValuesList);
                        return;
                    }
                    //deselect all
                    foreach (var key in ValuesList.Keys.ToList())
                    {
                        ValuesList[key] = false;
                    }
                    //select one
                    ValuesList[item.Title] = true;
                    //close
                    await DismissDialog(ValuesList);
                    return;
                }
                if (CanSelect || oldValue)
                {
                    //inverse
                    ValuesList[item.Title] = !oldValue;

                    //update frontend
                    item.Selected = !item.Selected;
                }
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
                throw;
            }

            UpdateTitle();




            // modify ValuesList



            //do not exit
            //await DismissDialog(index);
            Device.StartTimer(TimeSpan.FromMilliseconds(500), () =>
            {
                _tapped = false;
                return false;
            });

        }


        public bool ClickedChildWindow(Point click, View parent, View[] children)

        {
            var screenDensity = Core.DisplayDensity;
            //Get parent screen abs pos in pixels
            //We are using native code get absolute screen position
            var positionParent = DependencyService.Get<INativeTasks>().GetViewPositionOnScreen(parent);

            foreach (var child in children)
            {
                //Gets childs (hotspots) screen abs position in pixels
                var positionChild = DependencyService.Get<INativeTasks>().GetViewPositionOnScreen(child);

                //Gets childs (hotspots) rectangles, everything in pixels using screen density
                var rectChild = new Rect(positionChild, new Size((int)(child.Width * screenDensity), (int)(child.Height * screenDensity)));

                //Convert the finger XY to screen pos in pixels
                var positionClick = new Point((int)(positionParent.X + click.X * screenDensity), (int)(positionParent.Y + click.Y * screenDensity)); //absolute relative to screen

                if (rectChild.Contains(positionClick))
                    return true;
            }


            return false;
        }

        private bool _gridOnDownLock;
        private bool _gridOnDownFired;
        private bool _gridOnDownTracked;

        private void Grid_OnDown(object sender, TapEventArgs e)

        {
            if (e == null)
                return;

            if (_gridOnDownLock) return;
            _gridOnDownLock = true;

            try
            {
                DismissDialog(ValuesList);
            }
            catch (Exception exception)
            {
            }


            _gridOnDownLock = false;
        }



        public Action<Dictionary<string, bool>> CallbackListKeys { get; set; } = null;


        protected async Task DismissDialog(Dictionary<string, bool> ret)

        {
            try
            {
                if (NeedSelectMore)
                {
                    _gridOnDownLock = false;
                    _gridOnDownTracked = false;
                    return;
                }
                else
                {
                    CallbackListKeys(ret);

                    Dismiss();
                }
            }
            catch (Exception e)
            {
                //                System.Console.WriteLine(e);
            }

        }

        //-------------------------------------------------------------
        // SelectionColor
        //-------------------------------------------------------------
        private const string nameSelectionColor = "SelectionColor";
        public static readonly BindableProperty SelectionColorProperty = BindableProperty.Create(nameSelectionColor, typeof(Color), typeof(MultiSelectionListViewDrawn), AppColors.PrimaryHighlight); //, BindingMode.TwoWay
        public Color SelectionColor
        {
            get { return (Color)GetValue(SelectionColorProperty); }
            set { SetValue(SelectionColorProperty, value); }
        }

        private View CurrentSelectionGrid = null;
        private Color OriginalBackColor = Colors.Transparent;
        private TaskCompletionSource<Dictionary<string, bool>> _taskResult;

        private void OnDownCell(object sender, DownUpEventArgs e)
        {
            if (sender == null) return;

            //IsPressed = true;

            var cc = new Color();
            cc = SelectionColor;

            if (sender is View)
            {
                var sel1 = (View)sender;
                if (CurrentSelectionGrid != null)
                {
                    CurrentSelectionGrid.BackgroundColor = OriginalBackColor;
                }
                CurrentSelectionGrid = sel1;
                sel1.BackgroundColor = cc;
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    // Update the UI
                    //Grow(ss.SelectedIcon); //try catch inside
                });
            }


        }

        private void OnUpCell(object sender, DownUpEventArgs e)
        {
            if (sender == null) return;

            //            IsPressed = false;
            if (sender is View)
                CurrentSelectionGrid.BackgroundColor = OriginalBackColor;

        }

        private void SkiaControl_OnTapped(object? sender, SkiaControl.ControlTappedEventArgs e)
        {

            Debug.WriteLine("CLOSE");
            PopupPage.CloseAllPopups();

            //DismissDialog(ValuesList);

            //_ret = ret;
            //Dismiss();
        }

    }




}
