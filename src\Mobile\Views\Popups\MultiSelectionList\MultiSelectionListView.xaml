﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage
    x:Class="AppoMobi.Xam.MultiSelectionListView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="using:AppoMobi.Forms.Controls"
    xmlns:pages="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:DataType="xam:MultiSelectionListView"
    CanBeDismissedByTappingOutsideOfPopup="True"
    HorizontalOptions="Fill"
    IgnoreSafeArea="True"
    OverlayColor="#CC000000"
    VerticalOptions="Fill"
    Color="Transparent">

    <Grid HorizontalOptions="Fill" VerticalOptions="Fill">
        <Grid.GestureRecognizers>
            <TapGestureRecognizer Tapped="OnBackgroundTapped" />
        </Grid.GestureRecognizers>

        <Grid
            Margin="0,0"
            BackgroundColor="{x:Static xam:BackColors.OptionLine}"
            HorizontalOptions="End"
            IsClippedToBounds="False"
            MaximumWidthRequest="280"
            MinimumWidthRequest="100"
            RowDefinitions="32, Auto"
            RowSpacing="0"
            VerticalOptions="Center">

            <!--  HEADER  -->
            <xam:CGrid HorizontalOptions="Fill" InputTransparent="True">

                <svg:GradientBox
                    EndColor="{x:Static xam:BackColors.GradientStartNav}"
                    GradientOrientation="Horizontal"
                    HorizontalOptions="Fill"
                    StartColor="{x:Static xam:BackColors.GradientEndNav}"
                    VerticalOptions="Fill" />

                <BoxView
                    BackgroundColor="Transparent"
                    HeightRequest="38"
                    HorizontalOptions="Fill"
                    MinimumHeightRequest="24"
                    VerticalOptions="Center" />

                <BoxView
                    BackgroundColor="#33000000"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill" />

                <!--  header text  -->
                <Label
                    x:Name="cTitle"
                    Margin="20,0,8,0"
                    FontSize="15"
                    HorizontalOptions="Fill"
                    TextColor="WhiteSmoke"
                    VerticalOptions="Center" />

            </xam:CGrid>

            <xam:DebugScroll
                Grid.Row="1"
                Margin="0"
                HorizontalOptions="Fill"
                MaximumHeightRequest="500">

                <xam:NiftyDataStack
                    x:Name="cDataStack"
                    HorizontalOptions="Fill"
                    Spacing="0"
                    Tag="Popup">

                    <xam:NiftyDataStack.ItemTemplate>
                        <DataTemplate x:DataType="xam:SelectionListItem">

                            <controls:TouchStackLayout
                                x:Name="ControlMenu"
                                Down="OnDownCell"
                                DownCommandParameter="{Binding .}"
                                HorizontalOptions="Fill"
                                Spacing="0"
                                Tapped="OnTapped_Item"
                                Up="OnUpCell">

                                <!--  List Item  -->
                                <Grid
                                    Margin="0,16"
                                    ColumnSpacing="10"
                                    HorizontalOptions="Fill"
                                    IsClippedToBounds="True">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="30" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <Label
                                        Grid.Column="0"
                                        Grid.ColumnSpan="2"
                                        Margin="20,0,0,0"
                                        FontSize="17"
                                        IsVisible="{Binding Selected}"
                                        Text="&#x2713;"
                                        TextColor="{x:Static xam:TextColors.GreyDark}"
                                        VerticalOptions="Center" />

                                    <Label
                                        Grid.Column="1"
                                        Margin="8,0,8,0"
                                        FontSize="14"
                                        HorizontalOptions="Fill"
                                        LineBreakMode="CharacterWrap"
                                        MaxLines="2"
                                        Text="{Binding Title}"
                                        TextColor="{x:Static xam:TextColors.GreyDark}"
                                        VerticalOptions="Center" />
                                </Grid>

                                <!--  divider  -->
                                <BoxView
                                    BackgroundColor="LightGray"
                                    HeightRequest="0.5"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="End" />



                            </controls:TouchStackLayout>

                        </DataTemplate>
                    </xam:NiftyDataStack.ItemTemplate>

                </xam:NiftyDataStack>
            </xam:DebugScroll>

        </Grid>
    </Grid>


</pages:PopupPage>