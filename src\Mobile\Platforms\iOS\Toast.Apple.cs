﻿using AppoMobi.Mobile.Import.Abstractions;
using AppoMobi.Mobile.Import.Enums;
using System.Diagnostics;

namespace AppoMobi.Mobile
{
    public partial class Toast : IToastMessage
    {
        public void ShortAlert(string message)
        {
            Trace.WriteLine(message);

        }

        public void LongAlert(string message, ToastPosition position)
        {
            Trace.WriteLine(message);

        }
    }
}