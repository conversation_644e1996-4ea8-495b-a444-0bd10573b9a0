﻿using AppoMobi.Tenant;
using AppoMobi.Xam;

namespace AppoMobi.Pages;

public class WrapTabIncludedContent : ViewEnhancedNav
{
    public bool TabActive { get; set; }

    public override void OnTabActivated()
    {
        TabActive = true;

        base.OnTabActivated();

    }

    public override void OnTabDeactivated()
    {
        TabActive = false;

        base.OnTabDeactivated();
    }

    public override void OnLeftIcon1Clicked()
    {
        Globals.Values.AppRoot.ShowMenu();
    }

    protected override void SetDrawerMenuIcon()
    {
        LeftIcon1Source = "Images/close3a.png";
    }

}

public class WrapIncludedContent : ViewEnhancedNav
{
    public Type ContentType { get; protected set; }

    public WrapIncludedContent(System.Type contentType, string title, bool isFullScreen = false) : base()
    {
        Init();

        IsFullScreen = isFullScreen;

        ContentType = contentType;

        Title = title;

        ShowGoBack = true;
        SetGoBackNabIcon();
        OnGoBack = () =>
        {
            _ = App.Shell.ViewSwitcher.PopTab();
        };
        MenuInit = true;

        ReplaceContent(contentType);
    }

    void Init()
    {
        BackgroundColor = BackColors.Page;
        UseNavGradient = true;
    }

    public bool TabActive { get; set; }

    public override void OnTabActivated()
    {
        TabActive = true;

        base.OnTabActivated();

    }

    public override void OnTabDeactivated()
    {
        TabActive = false;

        base.OnTabDeactivated();
    }



    protected override void SetDrawerMenuIcon()
    {
        LeftIcon1Source = "Images/close3a.png";
    }

}
