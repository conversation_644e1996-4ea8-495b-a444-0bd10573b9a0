﻿using AppoMobi;
using Foundation;
using UIKit;

namespace AppoMobi.Mobile
{
    [Register("AppDelegate")]
    public class AppDelegate : MauiUIApplicationDelegate
    {
        public override bool FinishedLaunching(UIApplication application, NSDictionary launchOptions)
        {
            Core.IsIOS = true;

            Core.iBoldFonts = UIAccessibility.IsBoldTextEnabled;
            Core.ScreenSize = new Size((int)UIScreen.MainScreen.Bounds.Width, (int)UIScreen.MainScreen.Bounds.Height);
            Core.ScreenSizePixels = new Size((int)UIScreen.MainScreen.NativeBounds.Width, (int)UIScreen.MainScreen.NativeBounds.Height);

            //Core.DisplayDensity = (float)(UIScreen.MainScreen.NativeBounds.Width / UIScreen.MainScreen.Bounds.Width);

            //Core.StatusBarHeight = 20;
            //Core.StatusBarHeightRequest = Super.StatusBarHeight;
            Core.NavBarHeight = 47;////Sizes.NavBarHeight;//45;

            //if (Core.ScreenSizePixels.Height == 1136)
            //    Settings.Current.iModel = "iPhone 5 or 5S or 5C";
            //else if (Core.ScreenSizePixels.Height == 1334)
            //    Settings.Current.iModel = "iPhone 6/6S/7/8";
            //else if (Core.ScreenSizePixels.Height == 2208)
            //    Settings.Current.iModel = "iPhone 6+/6S+/7+/8+";
            //else
            //if (Core.ScreenSizePixels.Height == 2436)
            //    Settings.Current.iModel = "iPhone X";
            ////            1792 XR
            //else
            //    Settings.Current.iModel = "unknown";


            //DONT DELETE!!!!
            var cache = Settings.Current.iShowTabsText;

            return base.FinishedLaunching(application, launchOptions);
        }

        protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();
    }
}
