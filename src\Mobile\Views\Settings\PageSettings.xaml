﻿<?xml version="1.0" encoding="utf-8" ?>

<pages:PageCustomized
    x:Class="AppoMobi.PageSettings"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:controls="clr-namespace:AppoMobi.Controls"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:mobile="clr-namespace:AppoMobi.Mobile"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    Title="{x:Static resX:ResStrings.Settings}"
    ios:Page.UseSafeArea="False"
    BackgroundColor="{x:Static appoMobi:AppColors.Primary}"
    IsFullScreen="True"
    NavigationPage.BackButtonTitle="{x:Static resX:ResStrings.GoBack}"
    NavigationPage.HasNavigationBar="False">

    <pages:PageEnhancedNav.InsertContent>
        <pages:IncludedContent
            Margin="3"
            Padding="8,7,8,0"
            HorizontalOptions="FillAndExpand">
            <VerticalStackLayout>

                <appoMobi:CardView
                    x:Name="cardInterface"
                    Padding="0,8,0,8"
                    BackgroundColor="{x:Static appoMobi:AppColors.WhiteTrans}">
                    <appoMobi:CardView.Margin>
                        <OnPlatform x:TypeArguments="Thickness">
                            <On Platform="iOS">0,0,0,7</On>
                            <On Platform="Android">0, 0, 0, 7</On>
                            <On Platform="Windows">0, 0, 0, 7</On>
                        </OnPlatform>
                    </appoMobi:CardView.Margin>
                    <StackLayout Spacing="0">

                        <!--<Image Source="door.jpg"
                               HorizontalOptions="FillAndExpand"/>-->


                        <gestures:LegacyGesturesStackLayout Spacing="0" Tapping="OnTapping_Dev">

                            <!--  LOGO  -->
                            <forms:CachedImage
                                Margin="0,12,0,0"
                                Aspect="AspectFit"
                                Error="CachedImage_OnError"
                                ErrorPlaceholder="sad.png"
                                HeightRequest="90"
                                HorizontalOptions="Center"
                                IsVisible="False"
                                LoadingPriority="Highest"
                                Source="logo.png"
                                VerticalOptions="Center"
                                WidthRequest="90" />

                            <ContentView Margin="0,8,0,0" />

                            <!--  startup salon page  -->
                            <!--
                          <StackLayout
                              Spacing="0"
                              IsVisible="False"
                              x:Name="controlFav">
                            <StackLayout
                                Margin="16,8,16,8"
                                HorizontalOptions="FillAndExpand"
                                Orientation="Horizontal">
                                <Label
                                    Margin="0,0,16,0"
                                    FontFamily="{StaticResource FontMain}"
                                    HorizontalOptions="FillAndExpand"
                                    Style="{StaticResource ListItemTextStyle_News}"
                                    Text="{resX:Translate SettingsStartFav}" />
                                <Switch
                                    x:Name="switchStartFav"
                                    HorizontalOptions="End"
                                    Toggled="SwitchStartFav_OnToggledwitchStartFav_OnToggled"
                                    VerticalOptions="Center" />
                            </StackLayout>
                          <BoxView HeightRequest="0.75" BackgroundColor="LightGray" HorizontalOptions="FillAndExpand" VerticalOptions="End"/>
                          </StackLayout>-->


                            <xam:OptionLine x:Name="OptionLanguage" OnTappedHandler="OnDown_optionLanguage" />

                            <xam:OptionLine x:Name="OptionIconsScreenAlwaysOn" OnTappedHandler="OnDown_OptionScreenAlwaysOn" />

                            <xam:OptionLine x:Name="OptionIconsText" OnTappedHandler="OnDown_OptionIconsText" />

                            <xam:OptionLine
                                x:Name="OptionStandartCameraFolder"
                                IsVisible="{x:Static mobile:MauiProgram.IsMobile}"
                                OnTappedHandler="OnDown_OptionStandartCameraFolder" />

                            <xam:OptionLine
                                x:Name="OptionUseGeo"
                                IsVisible="{x:Static mobile:MauiProgram.IsMobile}"
                                OnTappedHandler="OnDown_OptionUseGeo" />

                            <xam:OptionLine
                                x:Name="OptionTabs"
                                ActionDesc="{x:Static resX:ResStrings.Settings_FavsTabs}"
                                OnTappedHandler="OnTapped_Tabs" />


                            <xam:OptionLine x:Name="OptionAlwaysTutorial" OnTappedHandler="OnDown_OptionAlwaysTutorial" />

                            <!--<xam:OptionLine
                               OnTappedHandler="OnDown_OptionSilentPush"
                               x:Name="OptionSilentPush"/>-->


                            <xam:OptionLine x:Name="OptionTheme" OnTappedHandler="OnDown_Theme" />


                            <!--  GPS  -->
                            <controls:CButton
                                x:Name="BtnGPS"
                                Margin="12"
                                Padding="16,10,16,0"
                                IsVisible="False"
                                Tapped="OnTapped_BtnSystemGPS" />


                            <!--<Button
                                Margin="8,0,8,0"
                                Clicked="Button_OnClicked"
                                IsVisible="False"
                                Text="{resX:Translate ShowWelcomeSlides}" />-->


                            <xam:OptionLine
                                x:Name="ListFonts"
                                ActionDesc="Шрифты"
                                IsVisible="False"
                                OnTappedHandler="OnDown_Fonts" />


                            <xam:OptionLine
                                x:Name="btnColors"
                                ActionDesc="Цвета"
                                IsVisible="False"
                                OnTappedHandler="ButtonColors_OnClicked" />

                            <xam:OptionLine
                                x:Name="btnClearImagesCache"
                                ActionDesc="Clear Images Cache"
                                IsVisible="False"
                                OnTappedHandler="ButtonDev_ClearImagesCache" />


                            <!--  SYSTEM SETTINGS BUTTON  -->
                            <!--  SYSTEM  -->
                            <xam:OptionLine
                                x:Name="OptionSystem"
                                ActionDesc="{x:Static resX:ResStrings.BtnAppSettings}"
                                IsVisible="{x:Static mobile:MauiProgram.IsMobile}"
                                OnTappedHandler="OnTapped_BtnSystemSettings" />

                        </gestures:LegacyGesturesStackLayout>


                    </StackLayout>
                </appoMobi:CardView>

                <!--  dev footer  -->
                <ui:DevFooter VerticalOptions="End" />

                <Label
                    x:Name="cDebug"
                    Margin="0,8,0,2"
                    HorizontalOptions="Center"
                    IsVisible="False"
                    Text="ВЕРСИЯ ДЛЯ РАЗРАБОТКИ"
                    TextColor="Red" />
            </VerticalStackLayout>

        </pages:IncludedContent>
    </pages:PageEnhancedNav.InsertContent>

</pages:PageCustomized>