﻿using Android.App;
using Android.Content.PM;
using Android.OS;
using Android.Views;
using AndroidX.Core.View;
using AndroidX.Navigation;
using AppoMobi;
using AppoMobi.Droid;
//using AppoMobi.WheelsForms.Droid;
using ReuseCode.Models.Video;
using System.Diagnostics;
using Microsoft.Maui.Platform;
using Build = Android.OS.Build;
using View = Android.Views.View;

namespace AppoMobi.Mobile
{
    [Activity(Theme = "@style/MainSplashTheme", MainLauncher = true, 
        LaunchMode = LaunchMode.SingleTop, 
        ScreenOrientation = ScreenOrientation.SensorPortrait,
        ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity, Android.Views.View.IOnApplyWindowInsetsListener
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            Core.AndroidAPI = (int)Build.VERSION.SdkInt;

            //appomobi engine warmup
            Reuse.AfterOnCreate(this, savedInstanceState, Resources);

            //will work ONLY in fullscreen mode
            if (Build.VERSION.SdkInt >= BuildVersionCodes.Kitkat)
            {
                var contentView = FindViewById(Android.Resource.Id.Content);
                contentView.SetOnApplyWindowInsetsListener(this);
            }

        }


        private WindowInsets _returnInsets;

        public WindowInsets OnApplyWindowInsets(View v, WindowInsets insets)
        {
            if (_returnInsets == null) //once
            {
                var displayMetricsDensity = Resources.DisplayMetrics.Density;

                //save this to a singleton to be used by UI everywhere
                var ptsTopInset = insets.SystemWindowInsetTop / displayMetricsDensity;
                var ptsBottomInset = insets.SystemWindowInsetBottom / displayMetricsDensity;

                //here you could change insets for your app too.
                //for example you make a full screen app without a status bar:

                //insets = insets.ReplaceSystemWindowInsets(
                //    insets.SystemWindowInsetLeft,
                //    0,
                //    insets.SystemWindowInsetRight,
                //    insets.SystemWindowInsetBottom
                //);

                _returnInsets = insets;
            }
            return _returnInsets;
        }
    }




}
