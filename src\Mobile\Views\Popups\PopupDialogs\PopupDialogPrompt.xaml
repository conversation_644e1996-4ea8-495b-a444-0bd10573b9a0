﻿<?xml version="1.0" encoding="utf-8" ?>
<ui:NiftyPopupDialogBase
    x:Class="AppoMobi.NiftyPopupDialogPrompt"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:controls="clr-namespace:AppoMobi.Controls"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:touch="clr-namespace:AppoMobi.Touch"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    HorizontalOptions="Fill"
    IgnoreSafeArea="True"
    VerticalOptions="Fill"
    Color="Transparent">


    <!--<pages:PopupPage.Animation>
        <animations:ScaleAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"
            PositionIn="Center"
            PositionOut="Center"
            ScaleIn="1.0"
            ScaleOut="0.5" />
    </pages:PopupPage.Animation>-->

    <Grid>

        <views:Canvas
            HorizontalOptions="FillAndExpand"
            InputTransparent="True"
            VerticalOptions="FillAndExpand">

            <xam:SkiaScreenshot HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
                <views:SkiaControl.VisualEffects>

                    <views:BlurEffect Amount="4" />
                    <views:TintEffect Color="#33000000" />

                </views:SkiaControl.VisualEffects>
            </xam:SkiaScreenshot>

        </views:Canvas>

        <touch:LegacyGesturesFrame
            x:Name="ControlMessageContainer"
            Margin="20,0,20,10"
            Padding="16"
            BackgroundColor="{x:Static xam:BackColors.GradientEnd}"
            HorizontalOptions="Center"
            IsVisible="{Binding IsLoading}"
            Stroke="{x:Static appoMobi:AppColors.DividerNav}"
            StrokeShape="RoundRectangle 16,16,16,16"
            StrokeThickness="1"
            VerticalOptions="Center">
            <ScrollView>
                <StackLayout
                    HorizontalOptions="Center"
                    Spacing="16"
                    VerticalOptions="Center">

                    <Label
                        x:Name="cMessage"
                        FontSize="14"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Message}"
                        TextColor="White" />

                    <Grid HorizontalOptions="FillAndExpand">

                        <StackLayout
                            HorizontalOptions="Fill"
                            Orientation="Horizontal"
                            Spacing="16">

                            <!--  Button  -->
                            <controls:CButton
                                x:Name="btnYes"
                                Margin="0,5,0,0"
                                Tapped="OnCLicked_OK"
                                Text="{x:Static resX:ResStrings.Yes}" />

                            <!--  Button  -->
                            <controls:CButton
                                x:Name="btnNo"
                                Margin="0,5,0,0"
                                Tapped="OnCLicked_Cancel"
                                Text="{x:Static resX:ResStrings.No}" />

                        </StackLayout>

                    </Grid>


                </StackLayout>
            </ScrollView>

        </touch:LegacyGesturesFrame>



    </Grid>

</ui:NiftyPopupDialogBase>