﻿<?xml version="1.0" encoding="UTF-8" ?>
<Grid
    x:Class="AppoMobi.Mobile.Views.Navigation.DrawerMenuView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:navigation1="clr-namespace:AppoMobi.Maui.Navigation;assembly=AppoMobi.Maui.Navigation"
    x:DataType="navigation1:IAppShell"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="FillAndExpand">


    <!--<controls:GradientBox
                EndColor="{StaticResource ColorPrimary}"
                HorizontalOptions="Fill"
                StartColor="{StaticResource ColorPrimaryLight}"
                VerticalOptions="Fill" />-->

    <StackLayout
        x:DataType="navigation1:IAppShell"
        Spacing="0"
        VerticalOptions="FillAndExpand">

        <!--  STATUS BAR  -->
        <ContentView
            HeightRequest="50"
            StyleId="StatusBar"
            VerticalOptions="Start" />

        <!--<draw:SkiaImage
                    Aspect="AspectFit"
                    HeightRequest="80"
                    HorizontalOptions="Center"
                    Source="resource://Resources.Images.png.icon.png?assembly=AppoMobi.Forms"
                    VerticalOptions="Center"
                    WidthRequest="80" />-->

        <StackLayout
            x:Name="MenuItems"
            Margin="16,32,16,16"
            BindableLayout.ItemsSource="{Binding MenuItems}"
            Spacing="0"
            VerticalOptions="Start">

            <BindableLayout.ItemTemplate>

                <DataTemplate x:DataType="navigation1:MenuPageItem">

                    <!--
                        touch:TouchEffect.PressedBackgroundColor="{StaticResource ColorDarkenOverlay}"
                        touch:TouchEffect.CommandParameter="{Binding .}"
                    -->
                    <Grid
                        Margin="2"
                        touch:TouchEffect.CommandTapped="{Binding Source={x:Reference MenuItems}, Path=BindingContext.CommandMenuItemTapped}"
                        ColumnDefinitions="24,*"
                        ColumnSpacing="16"
                        HeightRequest="40"
                        VerticalOptions="Start">

                        <draw:Canvas VerticalOptions="Center">
                            <draw:SkiaSvg
                                HeightRequest="20"
                                SvgString="{Binding IconString}"
                                TintColor="White"
                                LockRatio="1" />
                        </draw:Canvas>

                        <Label
                            FontFamily="FaLight"
                            FontSize="18"
                            HorizontalOptions="Center"
                            Text="{Binding IconString}"
                            TextColor="#d4d4d4"
                            VerticalOptions="Center" />

                        <Label
                            Grid.Column="1"
                            FontFamily="FontTextBold"
                            FontSize="15"
                            Text="{Binding Title}"
                            TextColor="White"
                            VerticalOptions="Center" />

                    </Grid>

                </DataTemplate>

            </BindableLayout.ItemTemplate>
        </StackLayout>

        <!--<touch:Hotspot
                VerticalOptions="FillAndExpand"
                HorizontalOptions="Fill"
                TappedCommand="{Binding Source={x:Reference MenuItems}, Path=BindingContext.CommandCloseMenuTapped}"/>-->

    </StackLayout>

</Grid>