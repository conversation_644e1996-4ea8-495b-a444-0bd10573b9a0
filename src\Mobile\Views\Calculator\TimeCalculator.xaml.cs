﻿using AppoMobi.Calculator;
using AppoMobi.Forms.Common.ResX;
using AppoMobi.Models;
using AppoMobi.Nifty;

using AppoMobi.Tenant;

using AppoMobi.Xam;
using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Threading.Tasks;



namespace AppoMobi.UI
{
    public partial class TimeCalculator
    {


        public TimeCalculator()
        {
            InitializeComponent();

            if (Core.IsAndroid)
            {
                cHH.TranslationY = 4;
                cMM.TranslationY = 4;
                cSS.TranslationY = 4;
                if (Core.HideStatusBar)
                {
                    //cTotalMini.TranslationY = 24;
                }
            }
            else
            {
                cHH.TranslationY = 4;
                cMM.TranslationY = 4;
                cSS.TranslationY = 4;
                if (Settings.Current.iModel == "iPhone X")//iphoneX
                {
                    //     cTotalMini.TranslationY = 10;
                    cTotalMini.FontSize = TenantOptions.FontSizeCalcHistory;
                }
            }

            HardReset();

            BindingContext = this;

            bottomPaddingFullscreen.HeightRequest = Super.Screen.BottomInset;
        }


        private bool _Overflow;
        public bool Overflow
        {
            get { return _Overflow; }
            set
            {
                if (_Overflow != value)
                {
                    _Overflow = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _DivisionByNull;
        public bool DivisionByNull
        {
            get { return _DivisionByNull; }
            set
            {
                if (_DivisionByNull != value)
                {
                    _DivisionByNull = value;
                    OnPropertyChanged();
                }
            }
        }


        string myoperator;

        double firstNumber;//, secondNumber;

        //new

        private CalcTime _secondTime;
        public CalcTime secondTime
        {
            get { return _secondTime; }
            set
            {
                if (_secondTime != value)
                {
                    _secondTime = value;
                }
            }
        }

        public bool TimeEntryDisabled { get; set; }

        public CalcTime firstTime { get; set; }

        private CalcTime _TotalTime;
        public CalcTime TotalTime
        {
            get { return _TotalTime; }
            set
            {
                if (_TotalTime != value)
                {
                    _TotalTime = value;
                    OnPropertyChanged();
                    OnPropertyChanged("TotalTimeDesc");
                    OnPropertyChanged("TotalTimeMsDesc");

                    if (TotalTime.Positive)
                        cSign.IsVisible = false;
                    else
                        cSign.IsVisible = true;
                    OutputValueToDisplay(_TotalTime);
                }
            }
        }

        /// <summary>
        /// We land here after user settings are closed
        /// </summary>

        public void Redraw()

        {
            LastOperator = "=";
            SetupEntryMode("=");
            //ChangeEntryMode(SimpleMode);
        }

        private string _TotalTimeMsDesc;

        public string TotalTimeMsDesc

        {
            get { return String.Format("= {0} {1}", TotalTime.TotalMilliseconds, ResStrings.Millisecs); }
        }



        private string _TotalTimeDesc;

        public string TotalTimeDesc

        {
            get
            {
                if (!Overflow)
                {


                    return $"{TotalTime.Hours:00}:{TotalTime.Minutes:00}:{TotalTime.Seconds:00}";
                }
                return $"OVERFLOW {TotalTime.Hours:00}:{TotalTime.Minutes:00}:{TotalTime.Seconds:00}";
            }
        }

        protected CalcTimeHistoryEntry LastAdded;

        public ObservableCollection<CalcTimeHistoryEntry> History { get; set; } = new();

        protected void UpdateFirstValue()

        {
            try
            {
                if (SimpleMode)
                {
                    var maybeValue = cSimpleEntry.Text.Replace(",", ".").ToDouble();
                    firstNumber = maybeValue;
                }
                else
                {
                    var maybeSpan = new CalcTime(cHH.Value, cMM.Value, cSS.Value);
                    firstTime = maybeSpan;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }


        protected void UpdateSecondValueFromDisplay()

        {
            try
            {
                if (SimpleMode)
                {
                    var maybeValue = cSimpleEntry.Text.Replace(",", ".").ToDouble();
                    firstNumber = maybeValue;
                }
                else
                {
                    var maybeSpan = new CalcTime(cHH.Value, cMM.Value, cSS.Value);
                    secondTime = maybeSpan;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

        //*******************************************************************************
        public enum PressedButton
        //*******************************************************************************
        {
            None,
            Number,
            Operator,
            Clear,
            Percent,
            Other
        }


        protected PressedButton LastPressed { get; set; } = PressedButton.None;



        void OnPressedNumber(object sender, EventArgs e)

        {
            if (Overflow)
            {
                return;
            }

            if (LastOperator == "=" && SelectedTimeField == null)
            {
                SoftReset();
            }

            DivisionByNull = false;


            CalcButton button = (CalcButton)sender;


            string pressed = button.Text;
            //validation before button is pressed

            var moved = false;
            LastPressed = PressedButton.Number;

            if (SimpleMode)
            {
                //if (pressed == "." || pressed == ",")
                //{
                //    HasDot = true;
                //}

                if (!cSimpleEntry.Focused)
                    SelectSimpleField();

                cSimpleEntry.InputValue(pressed);
            }
            else
            {

                //entering time
                if (SelectedTimeField == null)
                {
                    //Keypad Right-To-Left Mode
                    KeypadMode = true;
                    //todo right to left
                    cHH.Reset();
                    cMM.Reset();
                    SelectField("SS");

                    //todo add DELETE BACK button
                    //                    SelectNextField();
                }
                else
                {
                    if (KeypadMode)
                    {

                        if (!CanInput(pressed))
                            return;

                        if (SelectedTimeField.Filled)
                        {
                            if (CheckCanMove())
                            {
                                MoveValues();
                                moved = true;
                            }
                            else
                            {
                                //cSS.Reset();
                                //just block input if time full
                                return;

                            }
                        }
                    }
                    else
                    {
                        if (SelectedTimeField.Filled)
                        {
                            SelectedTimeField.Reset();
                        }

                    }

                }

                if (SelectedTimeField == null)
                {
                    //lol unknown error
                    return;
                }

                /*
                //if the current result is 0 in text box then we will direct the calculator to exclude 0 when pressing buttons
                if (SelectedTimeField.Text == "0" || currentState < 0)//at first current state is 1
                {
                    SelectedTimeField.Text = "";//here the text value will be cleared when pressing button

                    if (currentState < 0) //at first current value is 1 so this condition is excluded
                        currentState *= -1;
                }
                */

                //add number to displayed text field
                if (moved)
                {
                    KeypadMode = true; //select field again
                    SelectField("SS");
                    SelectedTimeField.NeedReset = false;
                }
                var success = SelectedTimeField.InputValue(pressed);
                if (!success) return; //todo more

                if (SelectedTimeField.Filled)
                {
                    if (!KeypadMode)
                    {
                        //disabled
                        //cannot move if selected manually!

                        //SelectedTimeField.UnFocus();
                        //ReleaseSeparators();
                        //KeypadMode = true;
                    }
                    else
                    {
                        //deselect
                        if (!CheckCanMove())
                        {
                            SelectedTimeField.UnFocus();
                            ReleaseSeparators();
                            //SelectedTimeField = null;
                        }
                    }
                }
            }



            OperationLock = false;
        }

        private bool CheckCanMove(bool keypad = true)

        {
            if (ShowInputHours)
            {
                if (MovedInput > 3)
                    return false;
            }
            else
            {
                if (MovedInput > 1)
                    return false;
            }
            return true;
        }

        protected int MovedInput;



        protected bool CanInput(string number)

        {
            return true;

            var iNumber = number.ToInteger();
            var AddNumber = cSS.Text.Right(1);
            var iAddNumber = AddNumber.ToInteger() * 10;
            if (iAddNumber + iNumber > 60)
                return false;
            return true;
        }


        private void MoveValues()

        {
            //todo right to left

            var newHH = cHH.Text.Right(1) + cMM.Text.Left(1);
            cHH.Set(newHH.ToInteger());

            var newMM = cMM.Text.Right(1) + cSS.Text.Left(1);
            cMM.Set(newMM.ToInteger());

            var newSS = cSS.Text.Right(1);
            cSS.Reset();
            cSS.Set(newSS.ToInteger());

            MovedInput++;
        }


        void OnPressedOperator(object sender, EventArgs e)//event is called when the select operator is called 

        {
            if (Overflow)
            {
                return;
            }

            DivisionByNull = false;

            SelectField("");
            UpdateSecondValueFromDisplay();


            CalcButton button = (CalcButton)sender;
            string pressed = button.Text;
            myoperator = pressed;

            //todo validate operator

            if (myoperator == "%")
            {
                if (LastOperator == "+" || LastOperator == "-" || LastOperator == "×" || LastOperator == "÷")
                {
                    myoperator = LastOperator + myoperator;
                }
                else
                {
                    LastPressed = PressedButton.Operator;
                    return;
                }
            }

            if (!OperationLock)
            {
                UpdateSecondValueFromDisplay();
                if (CombiningOperator && !string.IsNullOrEmpty(LastOperator) && myoperator != "=")
                    CalculateTime(LastOperator);
                else
                {
                    CalculateTime(myoperator);
                }
                OperationLock = true;
            }

            if (!Overflow)
            {
                cOperator.Text = myoperator;
                SetupEntryMode(myoperator);
                LastPressed = PressedButton.Operator;
                LastOperator = myoperator;
            }
        }

        protected void SetupEntryMode(string myoperator)

        {
            var mode = false;
            switch (myoperator)
            {
            case @"×%":
            mode = true;
            break;
            case @"÷%":
            mode = true;
            break;
            case @"-%":
            mode = true;
            break;
            case @"+%":
            mode = true;
            break;
            case @"×":
            mode = true;
            break;
            case @"÷":
            mode = true;
            break;
            case @"%":
            mode = true;
            break;
            }
            ChangeEntryMode(mode);
        }

        private bool _SimpleMode;
        public bool SimpleMode
        {
            get { return _SimpleMode; }
            private set
            {
                if (_SimpleMode != value)
                {
                    _SimpleMode = value;
                    OnPropertyChanged();
                }
            }
        }


        protected bool ShowInputHours

        {
            get => Settings.Current.ShowInputHours;
            set => Settings.Current.ShowInputHours = value;
        }


        public void ChangeEntryMode(bool IsSimple = false)

        {
            MovedInput = 0;

            if (IsSimple)
            {
                cSimpleEntry.Reset();
                cSimpleEntry.IsVisible = true;

                cHH.IsVisible = false;
                cSeparatorHH.IsVisible = false;

                cMM.IsVisible = false;
                cSeparatorMM.IsVisible = false;

                cSS.IsVisible = false;
                cSeparatorSS.IsVisible = false;

                cSign.IsVisible = false;
            }
            else
            {
                cSimpleEntry.Reset();
                cSimpleEntry.IsVisible = false;

                cHH.Enabled = ShowInputHours;

                cHH.IsVisible = true;
                cSeparatorHH.IsVisible = true;

                cMM.IsVisible = true;
                cSeparatorMM.IsVisible = true;

                cSS.IsVisible = true;
                cSeparatorSS.IsVisible = true;

                if (TotalTime != null && !TotalTime.Positive)
                    cSign.IsVisible = true;
                else
                    cSign.IsVisible = false;
            }

            SimpleMode = IsSimple;
        }


        protected void UpdateSecondValue(CalcTime value)

        {
            try
            {
                var maybeSpan = new CalcTime(value.Hours, value.Minutes, value.Seconds);
                secondTime = maybeSpan;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }


        protected void LogHistory(string myoperator, CalcTime overrideTime = null)
        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var newItem = new CalcTimeHistoryEntry
                {
                    Operator = myoperator,
                    Time = secondTime,
                    Value = firstNumber,
                    IsTime = !SimpleMode
                };

                if (overrideTime == null)
                {
                    if (cHH.Value > 60 || cMM.Value > 60 || cSS.Value > 60)
                    {
                        newItem.Reserved = $"{cHH.Value:00}:{cMM.Value:00}:{cSS.Value:00}";
                    }
                }

                if (overrideTime != null)
                {
                    newItem.Time = overrideTime;
                    newItem.IsTime = true;
                }

                History.Insert(0, newItem);
                LastAdded = newItem;

                await Task.Delay(10);

                Debug.WriteLine($"[LOG] {newItem} => {cHistoryList.Height}");

                //if (DeviceInfo.Current.Platform == DevicePlatform.Android)
                //{
                //    await HistoryScroll.ScrollToAsync(0, cHistoryList.Height, false);
                //}
                //else
                //{
                //    await HistoryScroll.ScrollToAsync(cHistoryList, ScrollToPosition.End, true);
                //}

                //History.Selected = newItem;

            });


        }

        //   
        //protected CalcTimeHistoryEntry GetLastValue()
        //   
        //{
        //    CalcTimeHistoryEntry ret = null;
        //    if (History.Count>0)
        //        ret = History.Last();
        //    return ret;
        //}


        protected bool CombiningOperator { get; set; }



        protected CalcTime CalculateTime(string myoperator)

        {
            //var first = GetLastValue();

            CalcTime resultTime;
            if (LastAdded == null) // && !string.IsNullOrEmpty(LastOperator
            {
                firstTime = new CalcTime(secondTime.Hours, secondTime.Minutes, secondTime.Seconds);
                resultTime = firstTime;
                LogHistory(""); //operator will be empty for the first entry
                if (myoperator != "=")
                    CombiningOperator = true;
            }
            else
            {
                if (LastAdded == null)
                    firstTime = new CalcTime();
                else
                    firstTime = LastAdded.Time;
                resultTime = new CalcTime(secondTime.Hours, secondTime.Minutes, secondTime.Seconds);
                switch (myoperator)
                {
                case "÷%":
                var divider = TotalTime.TotalMilliseconds / 100.0 * firstNumber;
                if (divider != 0)
                {
                    resultTime = new CalcTime(firstTime.TotalMilliseconds / divider);
                    LogHistory(myoperator);
                }
                break;
                case "×%":
                resultTime = new CalcTime(firstTime.TotalMilliseconds / 100.0 * firstNumber);
                LogHistory(myoperator);
                break;
                case "+%":
                if (firstNumber != 0)
                {
                    resultTime = new CalcTime(TotalTime.TotalMilliseconds + TotalTime.TotalMilliseconds / 100 * firstNumber);
                    LogHistory(myoperator);
                }
                break;
                case "-%":
                resultTime = new CalcTime(TotalTime.TotalMilliseconds - TotalTime.TotalMilliseconds / 100 * firstNumber);
                LogHistory(myoperator);
                break;
                case "+":
                resultTime = firstTime + secondTime;
                LogHistory(myoperator);
                break;
                case "-":
                resultTime = firstTime - secondTime;
                LogHistory(myoperator);
                break;
                case @"×":
                resultTime = new CalcTime(firstTime.TotalMilliseconds * firstNumber);
                LogHistory(myoperator);
                break;
                case @"÷":
                if (firstNumber != 0)
                {
                    resultTime = new CalcTime(firstTime.TotalMilliseconds / firstNumber);
                    LogHistory(myoperator);
                }
                else
                {
                    //division by 0
                    DivisionByNull = true;
                }
                break;
                case "=":
                //apply lastOperator
                if (!string.IsNullOrEmpty(LastOperator) && LastOperator != "=")
                {
                    resultTime = CalculateTime(LastOperator);
                    if (CheckOverflow(resultTime))
                    {
                        cOperator.Text = myoperator;
                        return resultTime;
                    }
                    LogHistory(myoperator, resultTime);
                    var last = LastPressed;
                }
                else
                {
                    resultTime = secondTime;
                    //LogHistory(myoperator);
                    //CombiningOperator = false;
                    //OperationLock = false;

                }
                CombiningOperator = false;
                break;
                }
            }
            TotalTime = resultTime;
            cOperator.Text = myoperator;

            CheckOverflow(resultTime);

            return resultTime;
        }


        protected bool CheckOverflow(CalcTime resultTime)

        {
            if (DivisionByNull) return true;

            if (Math.Abs(resultTime.Hours) > 59) Overflow = true;
            return Overflow;
        }

        protected string LastOperator { get; set; }

        protected void OutputValueToDisplay(CalcTime time)
        {
            cHH.Set(time.Hours);
            cMM.Set(time.Minutes);
            cSS.Set(time.Seconds);
        }

        protected void HardReset()
        {
            Overflow = false;
            DivisionByNull = false;
            KeypadMode = true;

            SetupEntryMode("");

            LastOperator = "";
            cOperator.Text = "";


            firstNumber = 0;
            this.resultText.Text = "0";

            firstTime = new CalcTime();
            secondTime = new CalcTime();
            TotalTime = new CalcTime();

            SelectedTimeFieldTag = "";
            SelectedTimeField = null;

            History.Clear();
            LastAdded = null; //important

            CombiningOperator = false;
            OperationLock = false;

            cHH.Reset();
            cMM.Reset();
            cSS.Reset();

            MovedInput = 0;


            UpdateFirstValue();
            UpdateSecondValueFromDisplay();
        }


        void OnClear(object sender, EventArgs e)// this method is called when we press the AC button

        {
            KeypadMode = true; //select field again
            SelectField("SS");
            SelectedTimeField.NeedReset = false;

            if (LastPressed == PressedButton.Clear || Overflow)
            {
                //total reset
                HardReset();
                LastPressed = PressedButton.None;
            }
            else
            {
                //clear current input without history

                secondTime = new CalcTime();
                firstNumber = 0;
                cHH.Reset();
                cMM.Reset();
                cSS.Reset();

                MovedInput = 0;



                UpdateFirstValue();
                UpdateSecondValueFromDisplay();

                cOperator.Text = "";
                LastOperator = "";
                OperationLock = true;

                SetupEntryMode("");

                LastPressed = PressedButton.Clear;

                Redraw();
            }


        }

        protected void SoftReset()

        {
            if (Overflow) return;

            DivisionByNull = false;


            LastOperator = "";
            cOperator.Text = "";
            firstNumber = 0;
            this.resultText.Text = "0";
            firstTime = new CalcTime();
            secondTime = new CalcTime();
            TotalTime = new CalcTime();
            SelectedTimeFieldTag = "";
            SelectedTimeField = null;

            CombiningOperator = false;
            OperationLock = false;

            LastAdded = null;

        }


        public string SelectedTimeFieldTag { get; set; }



        public CalcTimeEntry SelectedTimeField
        {
            get;
            set;
        }




        private void OnEnteringTime(object sender, CalcTimeEntryTag e)

        {
            if (e == null) return;
            var tag = e.Tag;

            KeypadMode = false; //manual field selection

            OnEditingStart();

            SelectField(tag);

        }


        protected void SelectSimpleField()

        {
            //OnEditingStart();
            cSimpleEntry.FocusField();
        }

        public bool KeypadMode
        {
            get;
            protected set;
        }


        protected void SelectNextField()

        {
            UpdateSecondValueFromDisplay();

            if (ShowInputHours)
            {
                switch (SelectedTimeFieldTag)
                {
                case "HH":
                SelectField("MM");
                break;
                case "MM":
                SelectField("SS");
                break;
                case "SS":
                default:
                //we entered label first pressing key
                OnEditingStart();
                SelectField("HH");
                break;
                }
            }
            else
            {
                switch (SelectedTimeFieldTag)
                {
                case "HH":
                Toast.LongMessage("ERROR 123");
                break;
                case "MM":
                SelectField("SS");
                break;
                case "SS":
                default:
                //we entered label first pressing key
                OnEditingStart();
                SelectField("MM");
                break;
                }
            }

        }


        protected void OnEditingStart()

        {
            if (OperationLock)
            {
                OutputValueToDisplay(new CalcTime());
            }

        }

        public bool OperationLock { get; set; }



        protected void ReleaseSeparators()

        {
            cSeparatorHH.Activated = false;
            cSeparatorMM.Activated = false;
            cSeparatorSS.Activated = false;
        }


        protected void SelectField(string tag)

        {
            cSimpleEntry.UnFocus();

            SelectedTimeFieldTag = tag;

            //unfocus others
            switch (tag)
            {
            case "HH":
            SelectedTimeField = cHH;
            cMM.UnFocus();
            cSS.UnFocus();
            cSeparatorHH.Activated = true;
            cSeparatorMM.Activated = false;
            cSeparatorSS.Activated = false;
            break;
            case "MM":
            SelectedTimeField = cMM;
            cHH.UnFocus();
            cSS.UnFocus();
            cSeparatorHH.Activated = false;
            cSeparatorMM.Activated = true;
            cSeparatorSS.Activated = false;
            break;
            case "SS":
            SelectedTimeField = cSS;
            cHH.UnFocus();
            cMM.UnFocus();
            cSeparatorHH.Activated = false;
            cSeparatorMM.Activated = false;
            cSeparatorSS.Activated = true;
            break;
            default:
            SelectedTimeFieldTag = "Error";
            SelectedTimeField = null;
            cHH.UnFocus();
            cMM.UnFocus();
            cSS.UnFocus();
            cSeparatorHH.Activated = false;
            cSeparatorMM.Activated = false;
            cSeparatorSS.Activated = false;
            return;
            }
            SelectedTimeField.FocusField();
        }

        private void OnTimer(object sender, EventArgs e)

        {



            //todo
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                btnTimer.Release();

                if (Settings.Current.FirstTimeTimer)
                {
                    // first time help message
                    Settings.Current.FirstTimeTimer = false;
                    //var dialog = new PopupResult("This is the first time calculator started help message.", ResStrings.ButtonOk);
                    //await dialog.ShowAsync(false);
                }

                //launch timer page
                UpdateSecondValueFromDisplay();
                var pageTimer = new PageTimer("Hello params.", (int)secondTime.TotalMilliseconds / 1000,
                    ResStrings.ButtonOk);
                await pageTimer.ShowAsync(false);
            });

        }



    }
}
