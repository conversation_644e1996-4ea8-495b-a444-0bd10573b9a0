﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage
    x:Class="AppoMobi.PopupResultView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:pages="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    CanBeDismissedByTappingOutsideOfPopup="True"
    HorizontalOptions="Fill"
    IgnoreSafeArea="True"
    OverlayColor="#CC000000"
    VerticalOptions="Fill"
    Color="Transparent">

    <!--<pages:PopupPage.Animation>
        <animations1:ScaleAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"
            HasBackgroundAnimation="False"
            PositionIn="Center"
            PositionOut="Center"
            ScaleIn="1.0"
            ScaleOut="0.5" />
    </pages:PopupPage.Animation>-->

    <Grid HorizontalOptions="Fill" VerticalOptions="Fill">
        <Grid.GestureRecognizers>
            <TapGestureRecognizer Tapped="OnBackgroundTapped" />
        </Grid.GestureRecognizers>



        <Grid
            Margin="20,0,20,0"
            HorizontalOptions="Center"
            InputTransparent="True"
            VerticalOptions="Center">

            <!--<gestures:Grid
            Down="OnDown_Grid"
            VerticalOptions="FillAndExpand">-->

            <Border
                x:Name="ControlMessageContainer"
                Padding="16"
                BackgroundColor="{x:Static xam:BackColors.GradientEnd}"
                Stroke="{x:Static appoMobi:AppColors.PrimaryLight}"
                StrokeShape="RoundRectangle 16,16,16,16"
                StrokeThickness="1"
                WidthRequest="300">

                <StackLayout
                    HorizontalOptions="Center"
                    InputTransparent="True"
                    Spacing="16"
                    VerticalOptions="Center">

                    <!--  message  -->

                    <Label
                        x:Name="cMessage"
                        Margin="10"
                        FontSize="{x:Static xam:FontSizes.Small}"
                        HorizontalOptions="FillAndExpand"
                        HorizontalTextAlignment="Start"
                        InputTransparent="True"
                        Text="..."
                        TextColor="White" />


                </StackLayout>


            </Border>

        </Grid>

    </Grid>

</pages:PopupPage>