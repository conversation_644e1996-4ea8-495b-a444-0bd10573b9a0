﻿using System;
using AppoMobi.Tenant;

namespace AppoMobi.Pages
{
    public class WrapContent : PageCustomized
    {
        public Type ContentType { get; protected set; }

        public WrapContent(System.Type contentType, bool isFullScreen, bool hideNavigation) : base()
        
        {
            IsFullScreen = isFullScreen;
            NavigationVisible = !hideNavigation;

            //BackgroundColor = AppColors.ba;
            NavigationPage.SetHasNavigationBar(this, false);
            NavigationPage.SetBackButtonTitle(this, ResStrings.GoBack);

            ContentType = contentType;

            Title = TenantOptions.AppName;
            
            ReplaceContent(contentType);
        }

        public bool TabActive { get; set; }

        public override void OnTabActivated()
        {
            TabActive = true;

            base.OnTabActivated();

        }

        public override void OnTabDeactivated()
        {
            TabActive = false;

            base.OnTabDeactivated();
        }

        
        public WrapContent(System.Type contentType, string title, params object[] args) : base()
        
        {
            ContentType = contentType;

            Title = title;

            ReplaceContent(contentType, args);


        }
    }
}
