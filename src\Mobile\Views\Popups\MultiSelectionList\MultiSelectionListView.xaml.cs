﻿using AppoMobi.Touch;
using AppoMobi.Mobile.Views.Popups;
using DrawnUi.Draw;
using DrawnUi.Extensions;
using System.Diagnostics;
using Color = Microsoft.Maui.Graphics.Color;
using Point = Microsoft.Maui.Graphics.Point;
using Size = Microsoft.Maui.Graphics.Size;

namespace AppoMobi.Xam
{
    public partial class MultiSelectionListView : PopupPage
    {


        public void UpdateTitle()
        {
            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                cTitle.TranslationY = 1;
            }

            if (MultiselectMax < 1)
            {
                Title = _title;
                cTitle.Text = Title;
                return;
            }
            if (MultiselectMin < 1)
                Title = string.Format(_title, CountSelected, MultiselectMax);
            else
                Title = string.Format(_title, CountSelected, MultiselectMax, MultiselectMin);
            cTitle.Text = Title;
        }

        public int CountSelected

        {
            get { return MenuList.Count(x => x.Selected); }
        }

        public bool CanSelect

        {
            get
            {
                if (MultiselectMax < 1) return true;
                if (CountSelected < MultiselectMax) return true;
                return false;
            }
        }

        public bool NeedSelectMore

        {
            get
            {
                if (MultiselectMin < 1) return false;
                if (CountSelected < MultiselectMin) return true;
                return false;
            }
        }

        private int _MultiselectMin;
        public int MultiselectMin
        {
            get { return _MultiselectMin; }
            set
            {
                if (_MultiselectMin != value)
                {
                    _MultiselectMin = value;
                    OnPropertyChanged(nameof(MultiselectMin));
                    UpdateTitle();
                }
            }
        }

        private int _MultiselectMax;
        public int MultiselectMax
        {
            get { return _MultiselectMax; }
            set
            {
                if (_MultiselectMax != value)
                {
                    _MultiselectMax = value;
                    OnPropertyChanged(nameof(MultiselectMax));
                    UpdateTitle();
                }
            }
        }


        public bool Multiselect { get; set; }


        public NiftyObservableCollection<SelectionListItem> MenuList { get; } =
            new NiftyObservableCollection<SelectionListItem>();

        public Dictionary<string, bool> ValuesList;

        public string _title;



        public Task<Dictionary<string, bool>> Present()
        {
            _taskResult = new TaskCompletionSource<Dictionary<string, bool>>();

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(500), () =>
            {
                Open();

                //                MainThread.BeginInvokeOnMainThread(async () =>
                //              {




                //                    //if (DeviceInfo.Platform == DevicePlatform.WinUI)
                //                    //{
                //                    //    MainCanvas.HardwareAcceleration = HardwareAccelerationMode.Disabled;
                //                    //}
                //                    var page = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault();
                //                    if (page != null)
                //                    {
                //                        page.ShowPopup(this);
                //                    }
                //                    else
                //                    {
                //#if WINDOWS
                //                        PopupWindowsFix.PlatformShowPopup(this, App.Current.MainPage.FindMauiContext(true));
                //#else
                //                        App.Current.MainPage.ShowPopup(this);
                //#endif
                //                    }
                //                    //await PopupNavigation.Instance.PushAsync(this);

                //                    //if (DeviceInfo.Platform == DevicePlatform.WinUI)
                //                    //{
                //                    //    await Task.Delay(500);
                //                    //    MainCanvas.HardwareAcceleration = HardwareAccelerationMode.Enabled;
                //                    //}
                //            });
            });

            return _taskResult.Task;
        }

        protected override Task OnClosed(object? result, bool wasDismissedByTappingOutsideOfPopup,
            CancellationToken token = new CancellationToken())
        {
            result = ValuesList;

            Callback(ValuesList);

            if (_taskResult != null)
            {
                if (!_taskResult.Task.IsCompleted)
                    _taskResult.SetResult(this.ValuesList);
            }

            return base.OnClosed(result, wasDismissedByTappingOutsideOfPopup, token);
        }

        public bool DisableBackgroundClick { get; set; }
        public bool QuitOnBackPressed { get; set; }

        public MultiSelectionListView(Action<Dictionary<string, bool>> callback, string title,
            Dictionary<string, bool> list,
                string cancel,
                bool disableBackgroundclick = false,
                bool quitOnBackPressed = false)
        {
            InitializeComponent();

            DisableBackgroundClick = disableBackgroundclick;
            QuitOnBackPressed = quitOnBackPressed;

            var ii = 0;
            foreach (var item in list)
            {
                var add = new SelectionListItem();
                add.Title = item.Key;
                add.Selected = item.Value;
                ii++;
                MenuList.Add(add);
            }
            ValuesList = list;

            Callback = callback;

            _title = title;
            UpdateTitle();

            cDataStack.ItemsSource = MenuList;

            BindingContext = this;
        }


        private bool _tapped = false;
        //-------------------------------------------------------------
        private async void OnTapped_Item(object sender, TapEventArgs e)
        //-------------------------------------------------------------
        {
            //if (_tapped) return;
            //_tapped = true;

            _gridOnDownFired = false;

            Debug.WriteLine("[MULTISELECTION] Tapped");


            //var stack = (AppoMobi.Touch.StackLayout) sender;
            var item = (SelectionListItem)e.Sender.DownCommandParameter;

            //var item = (SelectionListItem)e.Sender.TappedCommandParameter;
            if (item == null) return;

            //todo process tap
            try
            {
                var oldValue = ValuesList[item.Title];
                if (!Multiselect)
                {
                    if (oldValue)
                    {
                        //already selected
                        await DismissDialog(ValuesList);
                        return;
                    }
                    //deselect all
                    foreach (var key in ValuesList.Keys.ToList())
                    {
                        ValuesList[key] = false;
                    }
                    //select one
                    ValuesList[item.Title] = true;
                    //close
                    await DismissDialog(ValuesList);
                    return;
                }
                if (CanSelect || oldValue)
                {
                    //inverse
                    ValuesList[item.Title] = !oldValue;

                    //update frontend
                    item.Selected = !item.Selected;
                }
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
                throw;
            }

            UpdateTitle();




            // modify ValuesList



            //do not exit
            //await DismissDialog(index);
            Device.StartTimer(TimeSpan.FromMilliseconds(500), () =>
            {
                _tapped = false;
                return false;
            });

        }


        private bool _gridOnDownLock;
        private bool _gridOnDownFired;
        private bool _gridOnDownTracked;


        public Action<Dictionary<string, bool>> Callback { get; set; } = null;


        protected async Task DismissDialog(Dictionary<string, bool> ret)
        {
            try
            {
                if (NeedSelectMore)
                {
                    _gridOnDownLock = false;
                    _gridOnDownTracked = false;
                }
                else
                {
                    Dismiss();
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }

        }

        //-------------------------------------------------------------
        // SelectionColor
        //-------------------------------------------------------------
        private const string nameSelectionColor = "SelectionColor";
        public static readonly BindableProperty SelectionColorProperty = BindableProperty.Create(nameSelectionColor, typeof(Color), typeof(MultiSelectionListView), AppColors.PrimaryHighlight); //, BindingMode.TwoWay
        public Color SelectionColor
        {
            get { return (Color)GetValue(SelectionColorProperty); }
            set { SetValue(SelectionColorProperty, value); }
        }

        private View CurrentSelectionGrid = null;
        private Color OriginalBackColor = Colors.Transparent;
        private TaskCompletionSource<Dictionary<string, bool>> _taskResult;

        private void OnDownCell(object sender, DownUpEventArgs e)
        {
            if (sender == null) return;

            //IsPressed = true;

            var cc = new Color();
            cc = SelectionColor;

            if (sender is View)
            {
                var sel1 = (View)sender;
                if (CurrentSelectionGrid != null)
                {
                    CurrentSelectionGrid.BackgroundColor = OriginalBackColor;
                }
                CurrentSelectionGrid = sel1;
                sel1.BackgroundColor = cc;
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    // Update the UI
                    //Grow(ss.SelectedIcon); //try catch inside
                });
            }


        }

        private void OnUpCell(object sender, DownUpEventArgs e)
        {
            if (sender == null) return;

            //            IsPressed = false;
            if (sender is View)
                CurrentSelectionGrid.BackgroundColor = OriginalBackColor;

        }

 
        private void OnBackgroundTapped(object? sender, TappedEventArgs e)
        {
            this.Dismiss();
        }
    }




}
