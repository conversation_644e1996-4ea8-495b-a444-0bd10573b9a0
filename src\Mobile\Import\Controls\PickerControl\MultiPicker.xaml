﻿<?xml version="1.0" encoding="UTF-8" ?>
<controls:OptionsPicker
    x:Class="AppoMobi.Forms.Controls.PickerControl.MultiPicker"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:controls="clr-namespace:AppoMobi.Libs.UI.Controls"
    xmlns:controls1="clr-namespace:AppoMobi.Forms.Controls"
    xmlns:converters="clr-namespace:AppoMobi.Xam.Converters"
    xmlns:input="clr-namespace:AppoMobi.Forms.Controls.Input"
    xmlns:pages="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:svg="clr-namespace:AppoMobi.Forms.Controls.Svg"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="ThisControl"
    HasSystemPadding="False">

    <!--<pages:PopupPage.Animation>
        <animations:FadeAnimation
            DurationIn="150"
            DurationOut="100"
            EasingIn="SinOut"
            EasingOut="SinIn"
             />
    </pages:PopupPage.Animation>-->


    <pages:PopupPage.Resources>

        <ControlTemplate
            x:Key="TestTemplated_DefaultTemplate"
            x:Name="DefaultTemplate">


            <Grid
                x:Name="ControlContainer"
                Margin="0"
                Padding="0"
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand">

                <Grid.Resources>

                    <Style
                        x:Key="BoxGradientAccent"
                        ApplyToDerivedTypes="True"
                        TargetType="svg:GradientBox">
                        <!--<Setter Property="StartXRatio" Value="0.0" />
                        <Setter Property="StartYRatio" Value="0.5" />
                        <Setter Property="EndXRatio" Value="1.0" />
                        <Setter Property="EndYRatio" Value="0.5" />-->
                        <Setter Property="StartColor" Value="{x:StaticResource ColorAccentLight}" />
                        <Setter Property="EndColor" Value="{x:StaticResource ColorAccent}" />
                    </Style>

                </Grid.Resources>

                <!--<svg:Screenshot
                Blur="5"
                DarkenColor="#800A112C"
                HorizontalOptions="FillAndExpand"
                IsVisible="{Binding Source={x:Static appoMobi:App.Settings}, Path=EnableBlur}"
                VerticalOptions="FillAndExpand" />-->

                <!--<BoxView BackgroundColor="#500A112C" IsVisible="{Binding Source={x:Static appoMobi:App.Settings}, Path=EnableBlur, Converter={StaticResource NotConverter}}" />-->

                <controls1:Hotspot
                    HeightRequest="{Binding Source={x:Reference ThisControl}, Path=HotspotTopHeight}"
                    TappedCommand="{Binding Source={x:Reference ThisControl}, Path=CommandInternalClose}"
                    VerticalOptions="Start" />

                <controls1:Hotspot
                    HeightRequest="{Binding Source={x:Reference ThisControl}, Path=HotspotBottomHeight}"
                    TappedCommand="{Binding Source={x:Reference ThisControl}, Path=CommandInternalClose}"
                    VerticalOptions="End" />

                <controls1:Hotspot
                    Margin="0,60,0,60"
                    HeightRequest="{Binding Source={x:Reference ThisControl}, Path=HotspotLeftHeight}"
                    HorizontalOptions="Start"
                    TappedCommand="{Binding Source={x:Reference ThisControl}, Path=CommandInternalClose}"
                    VerticalOptions="Center"
                    WidthRequest="{Binding Source={x:Reference ThisControl}, Path=HotspotLeftWidth}" />

                <controls1:Hotspot
                    Margin="0,60,0,60"
                    HeightRequest="{Binding Source={x:Reference ThisControl}, Path=HotspotLeftHeight}"
                    HorizontalOptions="End"
                    TappedCommand="{Binding Source={x:Reference ThisControl}, Path=CommandInternalClose}"
                    VerticalOptions="Center"
                    WidthRequest="{Binding Source={x:Reference ThisControl}, Path=HotspotLeftWidth}" />


                <Frame
                    x:Name="ControlFrame"
                    Margin="60,60,60,60"
                    Padding="0"
                    BackgroundColor="{x:StaticResource ColorPaperCard}"
                    BorderColor="{x:StaticResource ColorPrimaryDark}"
                    CornerRadius="8"
                    HasShadow="False"
                    HorizontalOptions="Center"
                    IsClippedToBounds="True"
                    VerticalOptions="CenterAndExpand"
                    WidthRequest="300">

                    <StackLayout
                        BackgroundColor="{x:Static xam:BackColors.OptionLine}"
                        HorizontalOptions="Fill"
                        Spacing="0"
                        VerticalOptions="Start">

                        <!--  HEADER  -->
                        <!--  todo description?  -->

                        <Grid
                            HeightRequest="46"
                            RowSpacing="0"
                            VerticalOptions="Start">

                            <!--  background color  -->
                            <!--<BoxView HorizontalOptions="Fill" VerticalOptions="Fill">
                            <BoxView.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                    <GradientStop Offset="0.1" Color="{x:StaticResource ColorAccentLight}" />
                                    <GradientStop Offset="1.0" Color="{x:StaticResource ColorAccent}" />
                                </LinearGradientBrush>
                            </BoxView.Background>
                        </BoxView>-->

                            <svg:GradientBox
                                x:Name="GradientBackground"
                                EndColor="{StaticResource ColorPrimary}"
                                HorizontalOptions="Fill"
                                StartColor="{StaticResource ColorPrimaryLight}"
                                Style="{StaticResource BoxGradientAccent}"
                                VerticalOptions="Fill" />


                            <!--<Label
                            x:Name="IconClose"
                            Margin="20,0"
                            FontFamily="FaSolid"
                            FontSize="16"
                            HorizontalOptions="End"
                            Text="{x:Static fonts:FaPro.TimesCircle}"
                            TextColor="{StaticResource ColorPrimaryLight}"
                            VerticalOptions="Center" />-->

                            <Label
                                Margin="20,0,50,0"
                                FontFamily="{x:StaticResource FontTextBold}"
                                FontSize="14"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Reference ThisControl}, Path=Title}"
                                TextColor="{x:StaticResource ColorPaper}"
                                VerticalOptions="Center" />

                            <!--  divider  -->
                            <!--<BoxView
                            BackgroundColor="{StaticResource ColorPaperSecondary}"
                            HeightRequest="1"
                            HorizontalOptions="Fill"
                            VerticalOptions="End" />-->

                            <!--<touch:Hotspot TappedCommand="{Binding Source={x:Reference ThisControl}, Path=CommandInternalClose}" TransformView="{x:Reference IconClose}" />-->

                            <controls1:Hotspot TappedCommand="{Binding Source={x:Reference ThisControl}, Path=CommandInternalClose}" />


                        </Grid>





                        <ScrollView>
                            <StackLayout
                                x:Name="cDataStack"
                                Margin="0,0,0,0"
                                BindableLayout.ItemsSource="{Binding Source={x:Reference ThisControl}, Path=ItemsSource}"
                                Spacing="0.5"
                                VerticalOptions="Start">
                                <BindableLayout.ItemTemplate>
                                    <DataTemplate>


                                        <!--  List Item  -->
                                        <Grid
                                            HeightRequest="48"
                                            HorizontalOptions="FillAndExpand"
                                            RowDefinitions="47,1"
                                            VerticalOptions="Start">

                                            <!--  divider  -->
                                            <BoxView
                                                Grid.Row="1"
                                                BackgroundColor="{StaticResource ColorPaperSecondary}"
                                                HeightRequest="1"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="Start" />

                                            <StackLayout
                                                Margin="20,0"
                                                HeightRequest="47"
                                                Orientation="Horizontal"
                                                Spacing="8"
                                                VerticalOptions="Start">


                                                <StackLayout.GestureRecognizers>
                                                    <TapGestureRecognizer
                                                        Command="{Binding Source={x:Reference ThisControl}, Path=CommandInternalItemTapped}"
                                                        CommandParameter="{Binding .}" />
                                                </StackLayout.GestureRecognizers>

                                                <input:TemplatedCheck
                                                    Checked="{Binding Selected}"
                                                    IsVisible="{Binding Source={x:Reference ThisControl}, Path=Multiselect}"
                                                    VerticalOptions="Center" />

                                                <!--<Label
                                            Grid.Column="0"
                                            Margin="12,0,0,0"
                                            FontSize="17"
                                            IsVisible="{Binding Selected}"
                                            Text="&#x2713;"
                                            TextColor="{x:Static xam:TextColors.GreyDark}"
                                            VerticalOptions="Center" />-->

                                                <Label
                                                    Margin="0,0,4,0"
                                                    FontSize="14"
                                                    LineBreakMode="TailTruncation"
                                                    MaxLines="1"
                                                    Text="{Binding Title}"
                                                    VerticalOptions="Center">
                                                    <Label.Triggers>
                                                        <DataTrigger
                                                            Binding="{Binding Selected}"
                                                            TargetType="Label"
                                                            Value="False">
                                                            <Setter Property="FontFamily" Value="{StaticResource FontText}" />
                                                        </DataTrigger>
                                                        <DataTrigger
                                                            Binding="{Binding Selected}"
                                                            TargetType="Label"
                                                            Value="True">
                                                            <Setter Property="FontFamily" Value="{StaticResource FontTextBold}" />
                                                        </DataTrigger>
                                                    </Label.Triggers>
                                                </Label>
                                            </StackLayout>



                                            <Label
                                                Margin="0,0,20,0"
                                                FontFamily="FaSolid"
                                                FontSize="16"
                                                HorizontalOptions="End"
                                                IsVisible="{Binding Source={x:Reference ThisControl}, Path=Multiselect, Converter={x:StaticResource NotConverter}}"
                                                TextColor="{StaticResource ColorAccent}"
                                                VerticalOptions="Center">
                                                <Label.Triggers>
                                                    <DataTrigger
                                                        Binding="{Binding Selected}"
                                                        TargetType="Label"
                                                        Value="False">
                                                        <Setter Property="Text" Value="" />
                                                    </DataTrigger>
                                                    <DataTrigger
                                                        Binding="{Binding Selected}"
                                                        TargetType="Label"
                                                        Value="True">
                                                        <Setter Property="Text" Value="{x:Static xam:FaPro.CheckCircle}" />
                                                    </DataTrigger>
                                                </Label.Triggers>
                                            </Label>

                                        </Grid>





                                    </DataTemplate>
                                </BindableLayout.ItemTemplate>
                            </StackLayout>
                        </ScrollView>

                        <!--  todo action button  -->






                    </StackLayout>

                </Frame>


            </Grid>


        </ControlTemplate>
    </pages:PopupPage.Resources>

</controls:OptionsPicker>