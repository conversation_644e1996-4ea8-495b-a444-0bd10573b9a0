﻿using AppoMobi.Forms.Content.Camera.Controls;
using DrawnUi.Camera;

namespace AppoMobi.Mobile.Views.Camera.Exposure
{
    /// <summary>
    /// Uses drawn SkiaCamera as default content
    /// </summary>
    public class ExposureCamera : SkiaLayout
    {

        public SkiaCamera Camera;

        public ExposureCamera()
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            BackgroundColor = Colors.Black;
        }

        protected override void CreateDefaultContent()
        {
            base.CreateDefaultContent();

            if (Camera == null)
            {
                Children = new List<SkiaControl>() { new SkiaCamera()
                {
                    Aspect = TransformAspect.AspectCover
                }.Assign(out Camera).Fill() };
            }
        }

        public override void OnDisposing()
        {
            base.OnDisposing();

            if (BindingContext is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }

        //public override void OnAppearing()
        //{
        //    base.OnAppearing();

        //    if (BindingContext is ICameraViewModel vm)
        //    {
        //        vm.AttachCamera(_camera);

        //        vm.OnAppearing();
        //    }
        //}

        volatile bool _sVisible;

        public override void OnAppearing()
        {
            base.OnAppearing();

            Camera?.ResumeIfNeeded();
        }


        public override void OnAppeared()
        {
            _sVisible = true;

            base.OnAppeared();

            if (Camera != null)
            {
                if (Camera != null)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        Camera.IsOn = true;
                    });
                }
            }
        }

        public override void OnDisappeared()
        {
            _sVisible = false;

            base.OnDisappeared();

            Camera?.Stop();
        }

        public override void OnDisappearing()
        {
            base.OnDisappearing();

            _sVisible = false;

            Camera?.Stop();
        }
    }
}


