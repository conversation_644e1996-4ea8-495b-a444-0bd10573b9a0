using System;
using System.Windows.Input;
 


namespace AppoMobi.Models
{
/*    public class MenuPageItem : ObservableObject
     //****************************************************
	{


	    private bool _Separator;
	    public bool Separator
	    {
	        get { return _Separator; }
	        set
	        {
	            if (_Separator != value)
	            {
	                _Separator = value;
	                OnPropertyChanged();
	            }
	        }
	    }

        public string Title { get; set; }
		public string IconSource { get; set; }
	    
        public Color BackColor { get; set; } //{x:Static appoMobi:AppColors.Site_PanelXX}

        //new
        public string IconString { get; set; }

        public Action OnSelected { get; set; }

        public Type TargetType { get; set; }

        public dynamic TypeParameter { get; set; }
	    public Type ContentType { get; set; }

        public int Tab { get; set; } = 0;
        public bool NeedTransition { get; set; } = false;

        private bool _selectede=false;
		public bool Selected
		{
			get { return _selectede; }

			set
			{
				if (_selectede != value)
				{
					_selectede = value;
					OnPropertyChanged();
				}
			}
		}
	    public bool PseudoTab { get; set; }
        public bool Modal { get; set; }

        private bool _visible=true;
	    public bool Visible
	    {
	        get { return _visible; }

	        set
	        {
	            if (_visible != value)
	            {
	                _visible = value;
	                OnPropertyChanged();
	            }
	        }
	    }
        public string MyId { get; set; }
	    public string Url { get; set; }
        public string Key { get; set; }
	    /// <summary>
	    /// if module is disable the use this key to disable this item
	    /// </summary>
	    public string Module { get; set; }

        
	    public MenuPageItem()
        
	    {
	        BackColor = Colors.Transparent;
	    }
    }

    */

 //****************************************************
    public class MenuItem : ObservableObject
     //****************************************************
	{
		private string name;
		private string subtitle;

        private string _Name;
        public string Name
        {
            get { return _Name; }
            set
            {
                if (_Name != value)
                {
                    _Name = value;
                    OnPropertyChanged();
                }
            }
        }


        private string _Subtitle;
        public string Subtitle
        {
            get { return _Subtitle; }
            set
            {
                if (_Subtitle != value)
                {
                    _Subtitle = value;
                    OnPropertyChanged();
                }
            }
        }



        public string Kind { get; set; }
		public Type PageClass { get; set; }
		public string Icon { get; set; }
		public string Parameter { get; set; }

		//public AppPage Page { get; set; }
		public ICommand Command { get; set; }
	}

    public class TabbedMenuItem
    {
        public string Id { get; set; }
        /// <summary>
        /// Title in bottom tabbed bar
        /// </summary>
        public string NameInTabs { get; set; }
        /// <summary>
        /// Title in page header
        /// </summary>
        public string NameInHeader { get; set; }

        public string NavBarImage { get; set; }

        public Type PageClass { get; set; }

        public Type ContentClass { get; set; }

        public bool ContentIsFullScreen { get; set; }

        public bool HideNavigation { get; set; }

        public string IconSource { get; set; }

        public bool ZoomIcon { get; set; }
        public bool ZoomSelectedIcon { get; set; }

        //new
        public double IconSourceAjustScale { get; set; }
        public bool HiddenDefault { get; set; }


        public string IconString { get; set; }

        /// <summary>
        /// if module is disable the use this key to disable this item
        /// </summary>
        public string Module { get; set; }

        public string Platform { get; set; }

        
        public TabbedMenuItem()
        
        {
            Id= Guid.NewGuid().ToString();
        }
    }


    ////*************************************************************
    //public class IconFromSymbol
    ////*************************************************************
    //{
    //    public string Id { get; set; }

    //    public string IconString { get; set; } //the icon
    //    public string Name { get; set; } //subtitle for display..

    //    public bool ZoomIcon { get; set; }
    //    public bool ZoomSelectedIcon { get; set; }

    //    public string FontFamily { get; set; }
    //    public double Size { get; set; }

    //    
    //    public IconFromSymbol()
    //    
    //    {
    //        Id = Guid.NewGuid().ToString();
    //    }
    //}




}
