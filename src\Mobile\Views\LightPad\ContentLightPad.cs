﻿namespace AppoMobi.Main
{
    /// <summary>
    /// Temporary wrapper include drawn inside non-drawn
    /// </summary>
    public class ContentLightPad : ScreenCanvas
    {
        public ContentLightPad()
        {
            Create();
        }

        public ContentLightPad(IPageEnhancedNav daddy)
        {
            Create();

            Daddy = daddy;
        }

        void Create()
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            //RenderingMode = RenderingModeType.Accelerated;

            Content = new LightPad() { };
        }
    }

    /// <summary>
    /// Temporary wrapper include drawn inside non-drawn
    /// </summary>
    public class ContentWidgets : ScreenCanvas
    {
        public ContentWidgets()
        {
            Create();
        }

        public ContentWidgets(IPageEnhancedNav daddy)
        {
            Create();

            Daddy = daddy;
        }

        void Create()
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            Content = new ScreenWidgets() { };
        }
    }
}
