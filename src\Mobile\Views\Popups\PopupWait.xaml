﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage
    x:Class="AppoMobi.PopupWait"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:pages="clr-namespace:AppoMobi.Mobile.Views.Popups"
    Color="{x:Static appoMobi:AppColors.WhiteTransparent}">

    <ContentPage.Content>
        <StackLayout HorizontalOptions="Center" VerticalOptions="Center">




            <!--<appoMobi:CImage
                HeightRequest="40"
                WidthRequest="40"
                Aspect="AspectFit"
                Source="spinner.gif"
                VerticalOptions="CenterAndExpand"
                HorizontalOptions="Fill" />-->

        </StackLayout>
    </ContentPage.Content>
</pages:PopupPage>