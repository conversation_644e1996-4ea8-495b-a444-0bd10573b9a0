﻿using AppoMobi.Models;

namespace AppoMobi.Mobile.Views.Camera.Exposure
{
    public static class ExposureData
    {
        /// <summary>
        /// Standard shutter speed values for exposure meter
        /// </summary>
        public static readonly List<ValueItem> ShutterSpeeds = new List<ValueItem>
        {
            new ValueItem { Title = "1/8000", Value = 1.0 / 8000 },
            new ValueItem { Title = "1/6400", Value = 1.0 / 6400 },
            new ValueItem { Title = "1/5000", Value = 1.0 / 5000 },
            new ValueItem { Title = "1/4000", Value = 1.0 / 4000 },
            new ValueItem { Title = "1/3200", Value = 1.0 / 3200 },
            new ValueItem { Title = "1/2500", Value = 1.0 / 2500 },
            new ValueItem { Title = "1/2000", Value = 1.0 / 2000 },
            new ValueItem { Title = "1/1600", Value = 1.0 / 1600 },
            new ValueItem { Title = "1/1250", Value = 1.0 / 1250 },
            new ValueItem { Title = "1/1000", Value = 1.0 / 1000 },
            new ValueItem { Title = "1/800", Value = 1.0 / 800 },
            new ValueItem { Title = "1/640", Value = 1.0 / 640 },
            new ValueItem { Title = "1/500", Value = 1.0 / 500 },
            new ValueItem { Title = "1/400", Value = 1.0 / 400 },
            new ValueItem { Title = "1/320", Value = 1.0 / 320 },
            new ValueItem { Title = "1/250", Value = 1.0 / 250 },
            new ValueItem { Title = "1/200", Value = 1.0 / 200 },
            new ValueItem { Title = "1/160", Value = 1.0 / 160 },
            new ValueItem { Title = "1/125", Value = 1.0 / 125 },
            new ValueItem { Title = "1/100", Value = 1.0 / 100 },
            new ValueItem { Title = "1/80", Value = 1.0 / 80 },
            new ValueItem { Title = "1/60", Value = 1.0 / 60 },
            new ValueItem { Title = "1/50", Value = 1.0 / 50 },
            new ValueItem { Title = "1/40", Value = 1.0 / 40 },
            new ValueItem { Title = "1/30", Value = 1.0 / 30 },
            new ValueItem { Title = "1/25", Value = 1.0 / 25 },
            new ValueItem { Title = "1/20", Value = 1.0 / 20 },
            new ValueItem { Title = "1/15", Value = 1.0 / 15 },
            new ValueItem { Title = "1/13", Value = 1.0 / 13 },
            new ValueItem { Title = "1/10", Value = 1.0 / 10 },
            new ValueItem { Title = "1/8", Value = 1.0 / 8 },
            new ValueItem { Title = "1/6", Value = 1.0 / 6 },
            new ValueItem { Title = "1/5", Value = 1.0 / 5 },
            new ValueItem { Title = "1/4", Value = 1.0 / 4 },
            new ValueItem { Title = "0.3", Value = 0.3 },
            new ValueItem { Title = "0.4", Value = 0.4 },
            new ValueItem { Title = "0.5", Value = 0.5 },
            new ValueItem { Title = "0.6", Value = 0.6 },
            new ValueItem { Title = "0.8", Value = 0.8 },
            new ValueItem { Title = "1", Value = 1.0 },
            new ValueItem { Title = "1.3", Value = 1.3 },
            new ValueItem { Title = "1.6", Value = 1.6 },
            new ValueItem { Title = "2", Value = 2.0 },
            new ValueItem { Title = "2.5", Value = 2.5 },
            new ValueItem { Title = "3", Value = 3.0 },
            new ValueItem { Title = "4", Value = 4.0 },
            new ValueItem { Title = "5", Value = 5.0 },
            new ValueItem { Title = "6", Value = 6.0 },
            new ValueItem { Title = "8", Value = 8.0 },
            new ValueItem { Title = "10", Value = 10.0 },
            new ValueItem { Title = "13", Value = 13.0 },
            new ValueItem { Title = "15", Value = 15.0 },
            new ValueItem { Title = "20", Value = 20.0 },
            new ValueItem { Title = "25", Value = 25.0 },
            new ValueItem { Title = "30", Value = 30.0 }
        };

        /// <summary>
        /// Standard ISO values for exposure meter
        /// </summary>
        public static readonly List<ValueItem> IsoValues = new List<ValueItem>
        {
            new ValueItem { Title = "50", Value = 50 },
            new ValueItem { Title = "64", Value = 64 },
            new ValueItem { Title = "80", Value = 80 },
            new ValueItem { Title = "100", Value = 100 },
            new ValueItem { Title = "125", Value = 125 },
            new ValueItem { Title = "160", Value = 160 },
            new ValueItem { Title = "200", Value = 200 },
            new ValueItem { Title = "250", Value = 250 },
            new ValueItem { Title = "320", Value = 320 },
            new ValueItem { Title = "400", Value = 400 },
            new ValueItem { Title = "500", Value = 500 },
            new ValueItem { Title = "640", Value = 640 },
            new ValueItem { Title = "800", Value = 800 },
            new ValueItem { Title = "1000", Value = 1000 },
            new ValueItem { Title = "1250", Value = 1250 },
            new ValueItem { Title = "1600", Value = 1600 },
            new ValueItem { Title = "2000", Value = 2000 },
            new ValueItem { Title = "2500", Value = 2500 },
            new ValueItem { Title = "3200", Value = 3200 },
            new ValueItem { Title = "4000", Value = 4000 },
            new ValueItem { Title = "5000", Value = 5000 },
            new ValueItem { Title = "6400", Value = 6400 },
            new ValueItem { Title = "8000", Value = 8000 },
            new ValueItem { Title = "10000", Value = 10000 },
            new ValueItem { Title = "12800", Value = 12800 },
            new ValueItem { Title = "16000", Value = 16000 },
            new ValueItem { Title = "20000", Value = 20000 },
            new ValueItem { Title = "25600", Value = 25600 },
            new ValueItem { Title = "32000", Value = 32000 },
            new ValueItem { Title = "40000", Value = 40000 },
            new ValueItem { Title = "51200", Value = 51200 },
            new ValueItem { Title = "64000", Value = 64000 },
            new ValueItem { Title = "80000", Value = 80000 },
            new ValueItem { Title = "102400", Value = 102400 }
        };

        /// <summary>
        /// Standard aperture (f-stop) values for exposure meter
        /// </summary>
        public static readonly List<ValueItem> ApertureValues = new List<ValueItem>
        {
            new ValueItem { Title = "f/1.0", Value = 1.0 },
            new ValueItem { Title = "f/1.1", Value = 1.1 },
            new ValueItem { Title = "f/1.2", Value = 1.2 },
            new ValueItem { Title = "f/1.4", Value = 1.4 },
            new ValueItem { Title = "f/1.6", Value = 1.6 },
            new ValueItem { Title = "f/1.8", Value = 1.8 },
            new ValueItem { Title = "f/2.0", Value = 2.0 },
            new ValueItem { Title = "f/2.2", Value = 2.2 },
            new ValueItem { Title = "f/2.5", Value = 2.5 },
            new ValueItem { Title = "f/2.8", Value = 2.8 },
            new ValueItem { Title = "f/3.2", Value = 3.2 },
            new ValueItem { Title = "f/3.5", Value = 3.5 },
            new ValueItem { Title = "f/4.0", Value = 4.0 },
            new ValueItem { Title = "f/4.5", Value = 4.5 },
            new ValueItem { Title = "f/5.0", Value = 5.0 },
            new ValueItem { Title = "f/5.6", Value = 5.6 },
            new ValueItem { Title = "f/6.3", Value = 6.3 },
            new ValueItem { Title = "f/7.1", Value = 7.1 },
            new ValueItem { Title = "f/8.0", Value = 8.0 },
            new ValueItem { Title = "f/9.0", Value = 9.0 },
            new ValueItem { Title = "f/10", Value = 10.0 },
            new ValueItem { Title = "f/11", Value = 11.0 },
            new ValueItem { Title = "f/13", Value = 13.0 },
            new ValueItem { Title = "f/14", Value = 14.0 },
            new ValueItem { Title = "f/16", Value = 16.0 },
            new ValueItem { Title = "f/18", Value = 18.0 },
            new ValueItem { Title = "f/20", Value = 20.0 },
            new ValueItem { Title = "f/22", Value = 22.0 },
            new ValueItem { Title = "f/25", Value = 25.0 },
            new ValueItem { Title = "f/29", Value = 29.0 },
            new ValueItem { Title = "f/32", Value = 32.0 }
        };
    }
}
