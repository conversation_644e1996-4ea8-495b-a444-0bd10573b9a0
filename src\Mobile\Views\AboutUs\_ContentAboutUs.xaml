﻿<?xml version="1.0" encoding="UTF-8" ?>
<pages:IncludedContent
    x:Class="AppoMobi.ContentAboutUs"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:nifty="clr-namespace:AppoMobi.Nifty"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:skia="clr-namespace:AppoMobi.Forms.Controls.Skia"
    xmlns:ui="clr-namespace:AppoMobi.UI">

    <VerticalStackLayout Margin="2,0,2,0" Spacing="0">

        <!--  CARD 1  -->
        <appoMobi:CardView Margin="4" BackgroundColor="{x:Static appoMobi:AppColors.Cards}">

            <StackLayout Margin="0,5,0,0" Spacing="0">

                <!--  image  -->
                <appoMobi:ImageResponsive x:Name="imgLogo" Margin="4,4,4,4" />


                <ui:IconsRow
                    x:Name="IconsList"
                    Margin="12,0,12,0"
                    BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                    HorizontalOptions="Start"
                    Tapped="NiftyList_OnTapped" />


                <ui:CollapsedLabel
                    x:Name="cCollapsedLabel"
                    Margin="12,0,12,8"
                    CutSize="999999"
                    Text="{Binding Info.Strings.AboutCompanyDesc}"
                    TextColor="{x:Static appoMobi:AppColors.CardsHeaderText}" />


                <!--<Button
              x:Name="btn3"
              Margin="6,2,6,6"
              IsVisible="False"
              Clicked="On_PartnersLogin"
              Font="Small"
              HorizontalOptions="FillAndExpand"
              Text="{resX:Translate ButtonProPartners}" />-->

            </StackLayout>
        </appoMobi:CardView>


        <!--  LOGO  -->
        <!--
    <appoMobi:CardView

        IsVisible="False"
        BackgroundColor="{x:Static appoMobi:AppColors.Cards}" VerticalOptions="Start">
        <appoMobi:CardView.Margin>
            <OnPlatform x:TypeArguments="Thickness">
                <On Platform="iOS">0, 6, 0, 7</On>
                <On Platform="Android">0, 0, 0, 0</On>
                <On Platform="Windows">0, 0, 0, 0</On>
            </OnPlatform>
        </appoMobi:CardView.Margin>


        <StackLayout
            Margin="10,25,10,20"
            HorizontalOptions="FillAndExpand"
            Spacing="4">
            <appoMobi:CImage
              x:Name="imgSubLogo"
              Aspect="AspectFit"
              HeightRequest="50"
                VerticalOptions="FillAndExpand"
              HorizontalOptions="FillAndExpand"
               />
            <Label
              FontFamily="{StaticResource FontMainL}"
                x:Name="txtSubLogo"
              HorizontalTextAlignment="Center"
                Style="{StaticResource InfoSubLogoTextStyle}"
             />
        </StackLayout>



    </appoMobi:CardView>-->


        <!--  Contact Us  -->
        <appoMobi:CardView
            Margin="6"
            Padding="0,0,0,8"
            BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
            IsVisible="False">
            <StackLayout Spacing="0">

                <appoMobi:LabelSection
                    Margin="2,8,0,8"
                    InputTransparent="True"
                    Text="{x:Static resX:ResStrings.OurContacts}"
                    TextColor="{x:Static appoMobi:AppColors.CardsHeaderText}" />

                <appoMobi:HeaderDivider />

                <nifty:NiftyIconedList
                    x:Name="NiftyList"
                    BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
                    Tapped="NiftyList_OnTapped" />

            </StackLayout>
        </appoMobi:CardView>

        <!--  See On Map  -->
        <appoMobi:CardView
            x:Name="cardMap"
            Margin="6"
            Padding="0,0,0,8"
            BackgroundColor="{x:Static appoMobi:AppColors.Cards}"
            IsVisible="False">
            <StackLayout Spacing="0">

                <appoMobi:LabelSection
                    Margin="2,8,0,8"
                    Text="{x:Static resX:ResStrings.ShowOnMap}"
                    TextColor="{x:Static appoMobi:AppColors.CardsHeaderText}">
                    <appoMobi:LabelSection.GestureRecognizers>
                        <TapGestureRecognizer Tapped="ShowMapTapped" />
                    </appoMobi:LabelSection.GestureRecognizers>
                </appoMobi:LabelSection>

                <appoMobi:HeaderDivider />

                <nifty:NiftyIconedList x:Name="NiftyListMap" Tapped="NiftyList_OnTapped" />

            </StackLayout>
        </appoMobi:CardView>

        <!--  Links  -->
        <!--
    <appoMobi:CardView Padding="0,0,0,8" BackgroundColor="{StaticResource invisible}">
        <appoMobi:CardView.Margin>
            <OnPlatform x:TypeArguments="Thickness">
                <On Platform="iOS">0,0,0,8</On>
                <On Platform="Android">0, 0, 0, 0</On>
                <On Platform="Windows">0, 0, 0, 7</On>
            </OnPlatform>
        </appoMobi:CardView.Margin>
        <StackLayout Spacing="0">

            <appoMobi:LabelSection
              Margin="2,8,0,8"
              InputTransparent="True"
              Text="{resX:Translate Links}" />
            <appoMobi:HeaderDivider />

            <nifty:NiftyIconedList
              x:Name="NiftyListRes"
              LongPressed="NiftyList_OnLongPressed"
              Tapped="NiftyList_OnTapped" />

        </StackLayout>
    </appoMobi:CardView>-->

        <!--  dev footer  -->
        <ui:DevFooter Margin="6" />

    </VerticalStackLayout>

</pages:IncludedContent>