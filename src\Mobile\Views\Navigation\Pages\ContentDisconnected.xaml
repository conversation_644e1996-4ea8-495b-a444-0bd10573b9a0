﻿<?xml version="1.0" encoding="UTF-8"?>

<pages:IncludedContent
    x:Class="AppoMobi.Pages.ContentDisconnected"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:controls="clr-namespace:AppoMobi.Controls"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:ui="clr-namespace:AppoMobi.UI">

    <Grid
        x:Name="cContent"
        VerticalOptions="FillAndExpand">


        <controls:CButton
            Margin="16"
            Tapped="OnTapped_Reconect"
            Text="{x:Static resX:ResStrings.ButtonConnect}"
            VerticalOptions="Center" />


    </Grid>


</pages:IncludedContent>