﻿<?xml version="1.0" encoding="utf-8" ?>
<pages1:PopupPage
    x:Class="AppoMobi.UI.PopupLoading"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:pages1="clr-namespace:AppoMobi.Mobile.Views.Popups"
    Color="{x:Static appoMobi:AppColors.PrimaryDarkest}">

    <ContentPage.Content>
        <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">


            <!--  placeholder  -->
            <appoMobi:CImage
                Grid.Row="0"
                Aspect="AspectFit"
                HeightRequest="50"
                HorizontalOptions="Center"
                Source="spinner.gif"
                VerticalOptions="Center"
                WidthRequest="50" />


        </Grid>
    </ContentPage.Content>
</pages1:PopupPage>