﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage
    x:Class="AppoMobi.Launcher"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:pages="clr-namespace:AppoMobi.Mobile.Views.Popups"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    Color="#00000000">

    <ContentPage.Content>
        <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">

            <!--  wallpaper  -->
            <forms:CachedImage
                x:Name="imgLogo"
                Margin="0"
                Aspect="AspectFill"
                BitmapOptimizations="True"
                FadeAnimationEnabled="False"
                HorizontalOptions="FillAndExpand"
                LoadingPriority="Highest"
                VerticalOptions="FillAndExpand" />





            <Grid Margin="50,75,50,0" HorizontalOptions="Center">

                <!--  brand logo  -->
                <forms:CachedImage
                    x:Name="imgBrandLogo"
                    Aspect="AspectFit"
                    FadeAnimationEnabled="False"
                    HorizontalOptions="Center"
                    LoadingPriority="Highest"
                    VerticalOptions="StartAndExpand" />

                <StackLayout
                    x:Name="LoadingContainer"
                    Margin="0,70,0,0"
                    HorizontalOptions="Center"
                    Spacing="16">

                    <Label
                        x:Name="labelLoading"
                        FontFamily="sans-serif-light"
                        FontSize="19"
                        HorizontalOptions="Center"
                        Text="Идет загрузка"
                        TextColor="{x:Static xam:TextColors.Standart}" />

                    <xam:CProgressBar
                        x:Name="barProgress"
                        HorizontalOptions="Fill"
                        Progress="{Binding MyProgress}" />

                </StackLayout>
            </Grid>




        </Grid>
    </ContentPage.Content>
</pages:PopupPage>