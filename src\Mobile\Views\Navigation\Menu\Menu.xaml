<?xml version="1.0" encoding="UTF-8" ?>

<pages:PageMenuBase
    x:Class="AppoMobi.MenuPageX"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:controls="clr-namespace:AppoMobi.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:forms="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:gestures="clr-namespace:AppoMobi.Touch"
    xmlns:main="clr-namespace:AppoMobi.Main"
    xmlns:menu="clr-namespace:AppoMobi.UI"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:resX="clr-namespace:AppoMobi.Mobile.Import.Common.ResX"
    xmlns:transformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
    xmlns:valueConverters="clr-namespace:AppoMobi.Converters"
    xmlns:valueConverters1="clr-namespace:AppoMobi.Converters"
    xmlns:views="using:AppoMobi.Mobile.Views"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    Title="Menu"
    BackgroundColor="White"
    IconImageSource="menu.png"
    NavigationPage.BackButtonTitle="{x:Static resX:ResStrings.GoBack}">

    <draw:Canvas
        BackgroundColor="White"
        Gestures="Lock"
        HorizontalOptions="Fill"
        RenderingMode="Accelerated"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            Tapped="TappedClose"
            VerticalOptions="Fill">

            <!--  wallpaper  -->
            <draw:SkiaImage
                Margin="0"
                Aspect="AspectCover"
                HorizontalOptions="FillAndExpand"
                LoadSourceOnFirstDraw="False"
                Opacity="0.15"
                Source="Images/back.jpg"
                UseCache="Image"
                VerticalOptions="FillAndExpand" />

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Spacing="0"
                Type="Column"
                VerticalOptions="Fill">

                <!--  STATUS BAR  -->
                <main:StatusBarPlaceholder />

                <draw:SkiaScroll
                    Bounces="False"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">
                    <draw:SkiaScroll.Header>
                        <draw:SkiaControl
                            HeightRequest="20"
                            HorizontalOptions="Fill"
                            UseCache="Operations" />
                    </draw:SkiaScroll.Header>

                    <draw:SkiaLayout
                        x:Name="listView"
                        HorizontalOptions="Fill"
                        MeasureItemsStrategy="MeasureFirst"
                        Spacing="1"
                        Type="Column"
                        VerticalOptions="Start">

                        <draw:SkiaLayout.ItemTemplate>
                            <DataTemplate x:DataType="appoMobi:MenuPageContentItem">


                                <!--  CLICKABLE ROW - MENUITEM  -->

                                <!--  ITEM GRID  -->

                                <!--<appoMobi:CellMenuItem
                                     CellOnDown="OnCellDown"
                                     CellOnUp="OnCellUp"
                                     ItemTapped="OnTapped_Item" />-->

                                <draw:SkiaLayout
                                    AnimationTapped="Ripple"
                                    BackgroundColor="White"
                                    BindingContextChanged="CellContextChanged"
                                    ColumnDefinitions="40,*"
                                    ColumnSpacing="18"
                                    HorizontalOptions="Fill"
                                    RowDefinitions="Auto"
                                    RowSpacing="0"
                                    Tapped="MenuItemTapped"
                                    TouchEffectColor="#cc000000"
                                    Type="Grid"
                                    UseCache="Image">

                                    <views:DrawnFontIcon
                                        Margin="16,18,0,18"
                                        FontSize="22"
                                        HorizontalOptions="Center"
                                        InputTransparent="True"
                                        Text="{Binding IconString}"
                                        TextColor="{x:Static appoMobi:AppColors.BwGrey}"
                                        VerticalOptions="Center" />

                                    <draw:SkiaLabel
                                        Grid.Column="1"
                                        Margin="0,0,0,4"
                                        FontSize="17"
                                        InputTransparent="True"
                                        LineBreakMode="TailTruncation"
                                        Text="{Binding Title}"
                                        TextColor="{x:Static xam:TextColors.Standart}"
                                        VerticalOptions="FillAndExpand"
                                        VerticalTextAlignment="Center" />

                                </draw:SkiaLayout>

                            </DataTemplate>
                        </draw:SkiaLayout.ItemTemplate>
                    </draw:SkiaLayout>

                    <!--</Grid>-->

                </draw:SkiaScroll>

            </draw:SkiaLayout>

        </draw:SkiaLayout>
    </draw:Canvas>

</pages:PageMenuBase>