﻿<?xml version="1.0" encoding="UTF-8"?>

<Application
    x:Class="AppoMobi.Mobile.App"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:AppoMobi.Forms.Content.Camera.Controls"
    xmlns:controls1="clr-namespace:AppoMobi.Forms.Controls"
    xmlns:converters="clr-namespace:AppoMobi.Xam.Converters"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:input="clr-namespace:AppoMobi.Forms.Controls.Input"
    xmlns:local="clr-namespace:AppoMobi.Mobile"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    xmlns:xaml="clr-namespace:AppoMobi.Forms.Framework.Xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
                <xaml:Converters />
            </ResourceDictionary.MergedDictionaries>

            <!--#region COLORS-->
            <Color x:Key="ColorBlack">#000000</Color>
            <Color x:Key="ColorWhite">#FFFFFF</Color>
            <Color x:Key="ColorSuccess">#35D057</Color>
            <Color x:Key="ColorDanger">#DC1B48</Color>
            <Color x:Key="ColorInfo">#1BABDC</Color>

            <Color x:Key="SystemGray">#8E8E93</Color>
            <Color x:Key="SystemGray2">#AEAEB2</Color>
            <Color x:Key="SystemGray3">#C7C7CC</Color>
            <Color x:Key="SystemGray4">#D1D1D6</Color>
            <Color x:Key="SystemGray5">#E5E5EA</Color>
            <Color x:Key="SystemGray6">#F2F2F7</Color>

            <Color x:Key="ColorTextCellLight">#c6c6c6</Color>
            <Color x:Key="ColorTextCell">#a6a6a6</Color>

            <Color x:Key="ColorDimmer">#66000000</Color>
            <Color x:Key="ColorIconHeader">#22000000</Color>

            <Color x:Key="ColorOverlay">#11000000</Color>
            <Color x:Key="ColorDarkenOverlay">#33000000</Color>

            <Color x:Key="ColorDefault">#F5F6FC</Color>

            <Color x:Key="ColorPaper">#F5F6FC</Color>
            <Color x:Key="ColorPaperSecondary">#E1E5F6</Color>
            <Color x:Key="ColorPaperSecondaryDark">#dbdfef</Color>
            <Color x:Key="ColorPaperCard">#ffffff</Color>

            <Color x:Key="ColorLine">#22000000</Color>

            <Color x:Key="ColorPrimaryLight">#C2C8DE</Color>
            <Color x:Key="ColorPrimaryDark">#7894AF</Color>
            <Color x:Key="ColorPrimary">#A9BED1</Color>
            <Color x:Key="ColorAccent">#EE7600</Color>
            <Color x:Key="ColorAccentLight">#FF756D</Color>
            <Color x:Key="ColorAccentDark">#DC1B48</Color>

            <Color x:Key="ColorTitle">#2A2F44</Color>
            <Color x:Key="ColorText">#2A2F44</Color>
            <Color x:Key="ColorTextSecondary">#6E7388</Color>
            <Color x:Key="ColorTextGray">#7894AF</Color>
            <Color x:Key="ColorTextUI">#404040</Color>

            <Color x:Key="ColorUIElements">#d8d8d8</Color>

            <Color x:Key="ColorDimFocused">#06000000</Color>

            <OnPlatform x:Key="ColorStatusBar" x:TypeArguments="Color">
                <On Platform="iOS" Value="#20000000" />
                <On Platform="Android" Value="Transparent" />
            </OnPlatform>

            <!--  compat todo refactor  -->

            <x:String x:Key="FontText">FontText</x:String>
            <x:String x:Key="FontTextBold">FontTextBold</x:String>
            <x:String x:Key="FontText-Black">FontTextBold</x:String>
            <x:String x:Key="FontTextLight">FontTextLight</x:String>

            <x:String x:Key="FileFontDefault">FontTextBold</x:String>
            <x:String x:Key="FontTitle">FontTextBold</x:String>
            <x:String x:Key="FontText-UI">FontTextBold</x:String>
            <x:String x:Key="FontText-UI-Bold">FontTextBold</x:String>
            <x:String x:Key="FontText-UI-Black">FontTextBold</x:String>

            <Style ApplyToDerivedTypes="True" TargetType="Border">
                <Setter Property="Padding" Value="0" />
                <Setter Property="Stroke" Value="#CCCCCC" />
                <!--<Setter Property="StrokeShape" Value="Ellipse 22,22" />-->
            </Style>

            <Style x:Key="CameraButton" TargetType="controls1:TouchFrame">
                <Setter Property="Padding" Value="0" />
                <Setter Property="Stroke" Value="#33000000" />
                <Setter Property="Background" Value="#99FFFFFF" />
                <Setter Property="TintColor" Value="#99000000" />
                <Setter Property="Reaction" Value="Tint" />
                <Setter Property="StrokeShape" Value="Ellipse 22,22" />
                <Setter Property="HeightRequest" Value="44" />
                <Setter Property="WidthRequest" Value="44" />
                <Setter Property="HorizontalOptions" Value="Start" />
                <Setter Property="VerticalOptions" Value="Start" />
            </Style>

            <Style x:Key="CameraIcon" TargetType="xam:FontIconLabel">
                <Setter Property="FontSize" Value="22" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="HorizontalOptions" Value="Center" />
                <Setter Property="TextColor" Value="#88000000" />
            </Style>

            <Style ApplyToDerivedTypes="True" TargetType="input:EntryCentered">
                <Setter Property="CursorColor" Value="#EE7600" />
            </Style>


            <!--  DRAWN  -->

            <Style ApplyToDerivedTypes="True" TargetType="draw:SkiaLabel">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="FontFamily" Value="ui" />
                <Setter Property="TextColor" Value="White" />
            </Style>

            <!--#region SVG-->

            <x:String x:Key="SvgCamPresetColor">
                <![CDATA[ 
                            
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   viewBox="0 0 512 512"
   version="1.1"
   id="svg10"
   sodipodi:docname="imagecolored.svg"
   inkscape:version="0.92.4 (5da689c313, 2019-01-14)">



  <path
    d="M464 64H48a48 48 0 0 0-48 48v288a48 48 0 0 0 48 48h416a48 48 0 0 0 48-48V112a48 48 0 0 0-48-48zm-352 56a56 56 0 1 1-56 56 56 56 0 0 1 56-56zm336 264H64v-48l71.51-71.52a12 12 0 0 1 17 0L208 320l135.51-135.52a12 12 0 0 1 17 0L448 272z"
    class="fa-primary"
    id="path8" />

  <path
     d="M448 384H64v-48l71.51-71.52a12 12 0 0 1 17 0L208 320l135.51-135.52a12 12 0 0 1 17 0L448 272z"
     class="fa-secondary"
     id="path6"
     style="fill:#47a157;fill-opacity:1;fill-rule:nonzero;" />
  
  <circle
     style="fill:#f3811e;fill-opacity:1;fill-rule:nonzero;
     stroke:none;stroke-width:11.43999958;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     id="path821"
     cx="112.81358"
     cy="175.72882"
     r="56.40678" />

</svg>                                 

]]>
            </x:String>

            <x:String x:Key="SvgCamPresetMono">
                <![CDATA[ 
                            
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
  <path d="M464 448H48c-26.51 0-48-21.49-48-48V112c0-26.51 21.49-48 48-48h416c26.51 0 48 21.49 48 48v288c0 26.51-21.49 48-48 48zM112 120c-30.928 0-56 25.072-56 56s25.072 56 56 56 56-25.072 56-56-25.072-56-56-56zM64 384h384V272l-87.515-87.515c-4.686-4.686-12.284-4.686-16.971 0L208 320l-55.515-55.515c-4.686-4.686-12.284-4.686-16.971 0L64 336v48z"/>
</svg>                                   

]]>
            </x:String>


            <x:String x:Key="SvgStar">
                <![CDATA[ 
                            
<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M8 4.46738L8.68376 5.83308L9.15237 6.76907L10.1886 6.9175L11.658 7.12796L10.6143 8.1308L9.84726 8.86781L10.0296 9.91579L10.2822 11.3678L8.92024 10.662L8 10.1851L7.07976 10.662L5.71776 11.3678L5.9704 9.91579L6.15274 8.86781L5.38571 8.1308L4.34203 7.12796L5.81144 6.9175L6.84763 6.76907L7.31624 5.83308L8 4.46738ZM8 0L5.52786 4.9377L0 5.72949L4 9.57295L3.05573 15L8 12.4377L12.9443 15L12 9.57295L16 5.72949L10.4721 4.9377L8 0Z" fill="#C2C8DE"/>
</svg>                                    

]]>
            </x:String>

            <x:String x:Key="SvgLogo">
                <![CDATA[ 
                            
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 75.42 89.58"><title>Ресурс 2</title><path d="M59.59,35.16,43.82,41.53,37.45,25.76l15.77-6.37ZM33.3,40a8.51,8.51,0,0,1-17,0,8.69,8.69,0,0,1,8.51-8.58A8.69,8.69,0,0,1,33.3,40m38.53-6.07A33.89,33.89,0,1,0,37.94,67.78,33.89,33.89,0,0,0,71.83,33.89m2.84,52a2.94,2.94,0,1,1-2.93-3.09,2.91,2.91,0,0,1,2.93,3.09m.75,0a3.69,3.69,0,1,0-3.68,3.72,3.59,3.59,0,0,0,3.68-3.72M64.23,83H66.6v-.64H61.14V83h2.37V89.4h.72Zm-5.3,2.9A2.93,2.93,0,1,1,56,82.77a2.91,2.91,0,0,1,2.93,3.09m.76,0A3.69,3.69,0,1,0,56,89.58a3.6,3.6,0,0,0,3.69-3.72m-12.94.19h3.3v-.63h-3.3V83H50.3v-.64H46V89.4h.72Zm-10.12,0h3.3v-.63h-3.3V83h3.55v-.64H35.91V89.4h.72Zm-4.07-.19a2.93,2.93,0,1,1-2.93-3.09,2.91,2.91,0,0,1,2.93,3.09m.76,0a3.69,3.69,0,1,0-3.69,3.72,3.6,3.6,0,0,0,3.69-3.72M18.39,83h2.37v-.64H15.3V83h2.37V89.4h.72Zm-8.7,0H11c1.07,0,1.78.34,1.78,1.27s-.66,1.26-1.81,1.26H9.69Zm0,3.15h1.2l2,3.31h.88L11.66,86a1.84,1.84,0,0,0,1.81-1.82c0-1.38-1-1.89-2.42-1.89H9V89.4h.72Zm-6.3-3L5,86.88H1.83ZM1.56,87.54H5.22L6,89.4h.8l-3-7.08h-.7L0,89.4H.79Z" fill="#fcfcfc"/></svg>
]]>
            </x:String>

            <x:String x:Key="SvgTarget">
                <![CDATA[ 
                            
            <svg fill="#000000" height="800px" width="800px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
                 viewBox="0 0 500.004 500.004" xml:space="preserve">
            <g>
	            <g>
		            <path d="M179.746,238.28H11.718c-6.468,0-11.716,5.26-11.716,11.72c0,6.464,5.248,11.72,11.716,11.72h168.028
			            c6.456,0,11.716-5.252,11.716-11.72C191.458,243.54,186.202,238.28,179.746,238.28z"/>
	            </g>
            </g>
            <g>
	            <g>
		            <path d="M488.282,238.28H320.258c-6.464,0-11.716,5.256-11.716,11.72s5.248,11.72,11.716,11.72h168.024
			            c6.464,0,11.72-5.252,11.72-11.72C500.002,243.54,494.75,238.28,488.282,238.28z"/>
	            </g>
            </g>
            <g>
	            <g>
		            <path d="M250.002,308.54c-6.46,0-11.72,5.252-11.72,11.72v168.028c0,6.46,5.256,11.716,11.72,11.716s11.72-5.252,11.72-11.716
			            V320.256C261.722,313.792,256.466,308.54,250.002,308.54z"/>
	            </g>
            </g>
            <g>
	            <g>
		            <path d="M250.002,0c-6.46,0-11.72,5.252-11.72,11.72v168.024c0,6.46,5.256,11.712,11.72,11.712s11.72-5.248,11.72-11.712V11.72
			            C261.722,5.252,256.466,0,250.002,0z"/>
	            </g>
            </g>
            <g>
	            <g>
		            <circle cx="249.922" cy="250" r="11.796"/>
	            </g>
            </g>
            </svg>
                ]]>
            </x:String>


            <!--#endregion-->


            <!--#region CONVERTERS-->

            <converters:StringNotEmptyConverter x:Key="StringNotEmptyConverter" />
            <converters:NotConverter x:Key="NotConverter" />
            <converters:IsNotNullConverter x:Key="IsNotNullConverter" />
            <controls:OrientationToRotationConverter x:Key="OrientationToRotationConverter" />
            <xaml:CompareIntegersConverter x:Key="CompareIntegersConverter" />

            <!--#endregion-->

        </ResourceDictionary>
    </Application.Resources>
</Application>